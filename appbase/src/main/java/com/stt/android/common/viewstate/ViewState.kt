package com.stt.android.common.viewstate

import com.stt.android.common.ui.ErrorEvent

/**
 * Represents view state in combination with [ViewStateFragment]
 * @param T The data type that will be wrapped inside [ViewState]. Must be nullable.
 */
sealed class ViewState<out T>(val data: T?) {
    /**
     * State indicating that an error has occurred
     * @param errorEvent The error that occurred
     * @param data instance of [T]
     */
    class Error<T>(val errorEvent: ErrorEvent, data: T? = null) : ViewState<T>(data)

    /**
     * State indicating that loading is in progress
     * @param data instance of [T]
     */
    class Loading<T>(data: T? = null) : ViewState<T>(data)

    /**
     * State indicating that data has been loaded
     * @param data instance of [T]
     */
    class Loaded<T>(data: T?) : ViewState<T>(data)

    /**
     * Checks if the current state is [Loading]
     * @return true if the current view state is loading
     */
    fun isLoading(): Boolean = this is Loading

    /**
     * Checks if the current state is [Loaded]
     * @return true if the current view state is loaded
     */
    fun isLoaded() = this is Loaded

    /**
     * Checks if the current state is [Error]
     * @return true if the current view state is error
     */
    fun isFailure() = this is Error
}

inline fun <reified T> loading(data: T? = null) = ViewState.Loading<T>(data)
inline fun <reified T> loaded(data: T? = null) = ViewState.Loaded<T>(data)
inline fun <reified T> failure(errorEvent: ErrorEvent, data: T? = null) =
    ViewState.Error<T>(errorEvent, data)

suspend fun <T, U> ViewState<T>.flatMap(transform: suspend (T) -> ViewState<U>): ViewState<U> =
    when (this) {
        is ViewState.Error -> ViewState.Error(errorEvent)
        is ViewState.Loaded -> transform(data!!)
        is ViewState.Loading -> ViewState.Loading()
    }

/**
 * Map ViewState<[T]> to ViewState<[U]> by maintaining the state and applying the given
 * [transform] function to [ViewState.data] of the original ViewState if `data` is not null.
 */
suspend fun <T, U> ViewState<T>.mapViewState(
    transform: suspend (T) -> U
): ViewState<U> {
    val data = data?.let { transform(it) }
    return when (this) {
        is ViewState.Error -> ViewState.Error(errorEvent, data)
        is ViewState.Loaded -> ViewState.Loaded(data)
        is ViewState.Loading -> ViewState.Loading(data)
    }
}
