package com.stt.android.device.watchface

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Scaffold
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import com.stt.android.compose.util.setContentWithM3Theme
import dagger.hilt.android.AndroidEntryPoint

@OptIn(ExperimentalMaterialApi::class)
@AndroidEntryPoint
class SuuntoOnlineWatchFaceListFragment : Fragment() {

    private val viewModel by viewModels<SuuntoOnlineWatchFaceViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )

            setContentWithM3Theme {
                val viewData = viewModel.viewState.collectAsState().value
                val isRefreshing = viewData is SuuntoOnlineWatchFaceViewModel.ViewData.Loading

                Scaffold(
                    topBar = {
                        OnlineWatchFaceTopBar(
                            title = "watch face",
                            onUpPressed = {
                                with(findNavController()) {
                                    if (!popBackStack()) requireActivity().finish()
                                }
                            },
                        )
                    },
                ) { internalPadding ->
                    Box(
                        modifier = Modifier
                            .background(MaterialTheme.colorScheme.surface)
                            .padding(internalPadding)
                            .fillMaxWidth()
                    ) {
                        val pullRefreshState = rememberPullRefreshState(
                            refreshing = isRefreshing,
                            onRefresh = { viewModel.refresh() }
                        )

                        OnlineWatchFaceListScreen(
                            viewData = viewData,
                            modifier = Modifier
                                .fillMaxSize()
                                .pullRefresh(pullRefreshState, true),
                        )

                        PullRefreshIndicator(
                            refreshing = isRefreshing,
                            state = pullRefreshState,
                            modifier = Modifier.align(Alignment.TopCenter)
                        )
                    }
                }
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        viewModel.watchFaceClickEvent.observe(viewLifecycleOwner) { watchFaceListItem ->
            val action = SuuntoOnlineWatchFaceListFragmentDirections
                .actionWatchFaceListFragmentToWatchFaceDetailFragment(watchFaceListItem)
            findNavController().navigate(action)
        }
    }
}
