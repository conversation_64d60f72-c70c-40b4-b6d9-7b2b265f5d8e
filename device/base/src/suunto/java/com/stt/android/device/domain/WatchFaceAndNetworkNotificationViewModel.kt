package com.stt.android.device.domain

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.device.domain.watchface.IsWatchFaceSyncOngoingUseCase
import com.stt.android.device.watchface.WatchBusyState
import com.stt.android.device.watchface.WatchFaceStateListener
import com.stt.android.device.watchface.WatchFaceStateListenerImpl
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.utils.IsInternetAvailableUseCase
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import javax.inject.Inject

@HiltViewModel
class WatchFaceAndNetworkNotificationViewModel @Inject constructor(
    isSyncOngoingUseCase: IsWatchFaceSyncOngoingUseCase,
    isWatchBusyUseCase: IsWatchBusyUseCase,
    isWatchConnectedUseCase: IsWatchConnectedUseCase,
    currentWatchCapabilitiesUseCase: GetWatchCapabilitiesUseCase,
    isInternetAvailableUseCase: IsInternetAvailableUseCase,
) : ViewModel(),
    WatchFaceStateListener by WatchFaceStateListenerImpl(
        isSyncOngoingUseCase,
        isWatchBusyUseCase
    ) {

    private data class CustomToastData(
        val message: String,
        val actionText: String?,
        val isError: Boolean,
    )

    private val customToastData = MutableStateFlow<CustomToastData?>(null)
    private val customToastMutex = Mutex()

    val watchFaceAndNetworkNotificationState: StateFlow<WatchFaceAndNetworkNotificationState> =
        combine(
            currentWatchCapabilitiesUseCase.getCurrentCapabilitiesAsFlow(),
            isWatchConnectedUseCase.invoke(),
            watchBusyState,
            isInternetAvailableUseCase.isInternetAvailable(),
            customToastData
        ) { capabilitiesResult, watchConnected, busyState, internetAvailable, customToast ->
            WatchFaceAndNetworkNotificationState(
                internetAvailable = internetAvailable,
                watchPaired = capabilitiesResult.serial != null,
                watchBusy = busyState == WatchBusyState.BUSY,
                watchSyncing = busyState == WatchBusyState.SYNC_ONGOING,
                watchDisconnected = !watchConnected,
                isSuuntoRunWatchFaceSupported = capabilitiesResult.capabilities?.isRunWatchFaceSupported,
                customToastMessage = customToast?.message.takeIf { customToast?.isError == false },
                customToastActionText = customToast?.actionText,
                customErrorMessage = customToast?.message.takeIf { customToast?.isError == true }
            )
        }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.WhileSubscribed(),
                initialValue = WatchFaceAndNetworkNotificationState.DEFAULT
            )

    suspend fun showToastMessage(
        message: String,
        actionText: String? = null,
        isError: Boolean = false,
        durationMillis: Long = 4000L
    ) {
        // Use a mutex to make sure only one toast is shown at a time, similar to how
        // SnackbarHostState works
        customToastMutex.withLock {
            try {
                customToastData.value = CustomToastData(
                    message = message,
                    actionText = actionText,
                    isError = isError
                )

                delay(durationMillis)
            } finally {
                customToastData.value = null
            }
        }
    }
}
