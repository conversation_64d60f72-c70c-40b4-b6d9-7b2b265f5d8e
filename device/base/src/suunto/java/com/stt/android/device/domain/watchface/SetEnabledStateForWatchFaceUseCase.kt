package com.stt.android.device.domain.watchface

import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import javax.inject.Inject

class SetEnabledStateForWatchFaceUseCase @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
) {
    suspend fun updateAddToWatchState(id: String, addToWatchEnabled: Boolean) {
        localDataSource.updateAddToWatchEnabled(id, addToWatchEnabled)
    }
}
