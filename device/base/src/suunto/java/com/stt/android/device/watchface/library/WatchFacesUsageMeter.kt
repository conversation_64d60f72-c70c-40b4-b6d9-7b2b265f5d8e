package com.stt.android.device.watchface.library

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.LinearProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyLargeBold
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R

internal fun LazyListScope.watchFacesUsageMeter(
    usage: Int,
    maxUsage: Int,
    syncOngoing: Boolean,
    modifier: Modifier = Modifier,
) {
    item {
        WatchFacesUsageMeterContent(
            usage = usage,
            maxUsage = maxUsage,
            syncOngoing = syncOngoing,
            modifier = modifier
                .background(MaterialTheme.colorScheme.onPrimary)
                .padding(bottom = MaterialTheme.spacing.large),
        )
    }
}

@Composable
private fun WatchFacesUsageMeterContent(
    usage: Int,
    maxUsage: Int,
    syncOngoing: Boolean,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = MaterialTheme.spacing.medium),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.large)
    ) {
        Text(
            text = if (syncOngoing) {
                stringResource(R.string.suunto_plus_sports_watch_faces_syncing_to_watch)
            } else {
                stringResource(R.string.suunto_plus_features_usage_meter_only_watch_faces_title)
            },
            style = MaterialTheme.typography.bodyLargeBold
        )

        Row(
            horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.medium),
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.fillMaxWidth()
        ) {
            if (syncOngoing) {
                LinearProgressIndicator(
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .weight(1f)
                        .clip(RoundedCornerShape(2.dp))
                )
            } else if (usage >= maxUsage) {
                LinearProgressIndicator(
                    progress = 1f,
                    color = MaterialTheme.colorScheme.error,
                    modifier = Modifier
                        .weight(1f)
                        .clip(RoundedCornerShape(2.dp))
                )
            } else {
                LinearProgressIndicator(
                    progress = usage.toFloat() / maxUsage.toFloat(),
                    color = MaterialTheme.colorScheme.primary,
                    modifier = Modifier
                        .weight(1f)
                        .clip(RoundedCornerShape(2.dp))
                )
            }

            if (!syncOngoing) {
                Text(
                    text = buildString {
                        append(usage)
                        append("/")
                        append(maxUsage)
                    },
                    style = MaterialTheme.typography.bodyLargeBold
                )
            }
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun WatchFacesUsageMeterPreview() {
    M3AppTheme {
        WatchFacesUsageMeterContent(
            usage = 3,
            maxUsage = 10,
            syncOngoing = false,
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.large),
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true)
@Composable
private fun WatchFacesUsageMeterOnSyncingPreview() {
    M3AppTheme {
        WatchFacesUsageMeterContent(
            usage = 3,
            maxUsage = 10,
            syncOngoing = true,
            modifier = Modifier.padding(bottom = MaterialTheme.spacing.large),
        )
    }
}


