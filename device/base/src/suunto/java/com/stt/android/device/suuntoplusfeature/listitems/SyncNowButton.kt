package com.stt.android.device.suuntoplusfeature.listitems

import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.spring
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Button
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.isMaterial3
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.R as BaseR
import androidx.compose.material3.Button as M3Button
import androidx.compose.material3.Icon as M3Icon
import androidx.compose.material3.Text as M3Text

@Composable
fun SyncNowButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    if (isMaterial3()) {
        M3SyncNowButton(onClick = onClick, modifier = modifier)
    } else {
        M2SyncNowButton(onClick = onClick, modifier = modifier)
    }
}

@Composable
internal fun M2SyncNowButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var rotationCount by remember { mutableIntStateOf(0) }
    LaunchedEffect(Unit) {
        // Spin the icon once on initial composition
        rotationCount++
    }
    Button(
        onClick = {
            onClick()
            rotationCount++
        },
        shape = RoundedCornerShape(percent = 50),
        contentPadding = PaddingValues(
            start = MaterialTheme.spacing.small,
            end = MaterialTheme.spacing.smaller,
            top = MaterialTheme.spacing.xsmall,
            bottom = MaterialTheme.spacing.xsmall,
        ),
        modifier = modifier
    ) {
        val rotation by animateFloatAsState(
            animationSpec = spring(stiffness = Spring.StiffnessLow),
            targetValue = rotationCount * 180.0f
        )

        Icon(
            painter = painterResource(id = R.drawable.sync_fill),
            contentDescription = null,
            modifier = Modifier.graphicsLayer { rotationZ = rotation }
        )

        Text(
            text = stringResource(id = BaseR.string.device_ui_action_sync_now),
            modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall)
        )
    }
}

@Composable
internal fun M3SyncNowButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    var rotationCount by remember { mutableIntStateOf(0) }
    LaunchedEffect(Unit) {
        // Spin the icon once on initial composition
        rotationCount++
    }
    M3Button(
        onClick = {
            onClick()
            rotationCount++
        },
        shape = RoundedCornerShape(percent = 50),
        contentPadding = PaddingValues(
            start = MaterialTheme.spacing.small,
            end = MaterialTheme.spacing.smaller,
            top = MaterialTheme.spacing.xsmall,
            bottom = MaterialTheme.spacing.xsmall,
        ),
        modifier = modifier
    ) {
        val rotation by animateFloatAsState(
            animationSpec = spring(stiffness = Spring.StiffnessLow),
            targetValue = rotationCount * 180.0f
        )

        M3Icon(
            painter = painterResource(id = R.drawable.sync_fill),
            contentDescription = null,
            modifier = Modifier.graphicsLayer { rotationZ = rotation }
        )

        M3Text(
            text = stringResource(id = BaseR.string.device_ui_action_sync_now),
            modifier = Modifier.padding(start = MaterialTheme.spacing.xsmall)
        )
    }
}

@Preview
@Composable
private fun SyncNowButtonPreview() {
    AppTheme {
        SyncNowButton(onClick = {})
    }
}

@Preview
@Composable
private fun M3SyncNowButtonPreview() {
    M3AppTheme {
        M3SyncNowButton(onClick = {})
    }
}
