package com.stt.android.device.watchface

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.coroutines.runSuspendCatching
import com.stt.android.device.domain.watchface.OnlineWatchFaceUseCase
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.ui.utils.SingleLiveEvent
import com.suunto.connectivity.watchface.MdsWatchFace
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class SuuntoOnlineWatchFaceViewModel @Inject constructor(
    private val onlineWatchFaceUseCase: OnlineWatchFaceUseCase,
) : ViewModel() {

    sealed class ViewData {
        data object Loading : ViewData()

        data class Loaded(
            val data: OnlineWatchFaceViewData,
        ) : ViewData()

        data class Failed(val errorMsg: String) : ViewData()
    }

    private val _viewState: MutableStateFlow<ViewData> = MutableStateFlow(
        ViewData.Loading
    )
    val viewState: StateFlow<ViewData> = _viewState.asStateFlow()

    val watchFaceClickEvent = SingleLiveEvent<WatchFace>()

    init {
        fetchOnlineWatchFaceContainer()
    }

    private fun fetchOnlineWatchFaceContainer() = viewModelScope.launch(Dispatchers.IO) {
        runSuspendCatching {
            val onlineWatchFaceList = onlineWatchFaceUseCase.fetAllOnlineWatchFaces()
            val installedWatchFaceList = onlineWatchFaceUseCase.getInstalledWatchFaceList()
            Pair(onlineWatchFaceList, installedWatchFaceList)
        }.onSuccess { (watchFaceListContainer, installedWatchFaceList) ->
            Timber.d("getInstalledWatchFaces successful.")
            _viewState.value = ViewData.Loaded(
                OnlineWatchFaceViewData(
                    watchFaceListContainer = watchFaceListContainer,
                    installedWatchFaceList = installedWatchFaceList,
                    onWatchFaceInstall = ::installWatchFace,
                    onWatchUninstall = ::uninstallWatchFace,
                    onWatchFaceClick = ::onWatchFaceClick
                )
            )
        }.onFailure {
            Timber.w(it, "getInstalledWatchFaces failed.")
            _viewState.value = ViewData.Failed(it.message ?: "Unknown error")
        }
    }

    private fun installWatchFace(watchFace: WatchFaceEntity) = viewModelScope.launch {
        runSuspendCatching {
            onlineWatchFaceUseCase.startInstallOnlineWatchFace(watchFace)
        }.onSuccess {
            Timber.d("installWatchFace successful.")
        }.onFailure {
            Timber.w(it, "installWatchFace failed.")
        }
    }

    private fun uninstallWatchFace(watchFace: MdsWatchFace) = viewModelScope.launch {
        runSuspendCatching {
            onlineWatchFaceUseCase.uninstallWatchFace(watchFace.id)
        }.onSuccess {
            Timber.d("uninstallWatchFace successful.")
        }.onFailure {
            Timber.w(it, "uninstallWatchFace failed.")
        }
    }

    private fun onWatchFaceClick(watchFace: WatchFace) {
        watchFaceClickEvent.value = watchFace
    }

    fun refresh() {
        _viewState.value = ViewData.Loading
        fetchOnlineWatchFaceContainer()
    }
}
