package com.stt.android.device.watchface

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import coil3.compose.AsyncImage
import coil3.request.ImageRequest
import coil3.request.crossfade
import com.stt.android.compose.layout.ContentCenteringColumn
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.material3.bodyBold
import com.stt.android.compose.theme.material3.bodyMegaBold
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.PrimaryButton
import com.stt.android.device.R
import com.stt.android.device.domain.WatchFaceAndNetworkNotificationState
import com.stt.android.device.domain.WatchFaceAndNetworkNotificationStateComposable
import com.stt.android.device.domain.watchface.WatchFaceEntity
import com.stt.android.device.domain.watchface.WatchFaceStatus
import com.stt.android.device.watchface.note.WatchFaceDetailNotes
import com.stt.android.device.watchface.note.WatchFaceNote
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf

@Composable
fun OnlineWatchFaceDetailScreen(
    watchFace: WatchFaceEntity,
    watchFaceState: WatchFaceStatus,
    watchFaceNotes: ImmutableList<WatchFaceNote>,
    showInstallButton: Boolean,
    showSetAsCurrentWatchfaceButton: Boolean,
    enableInstallButton: Boolean,
    enableUninstallButton: Boolean,
    watchFaceAndNetworkNotificationState: WatchFaceAndNetworkNotificationState,
    onInstallClick: () -> Unit,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    onUninstallClick: () -> Unit,
    onNoteAction: (WatchFaceNote.NoteButtonAction) -> Unit,
    modifier: Modifier = Modifier,
    onCustomToastActionClick: () -> Unit = {},
    showWatchFaceStatusButton: Boolean = false,
) {
    Box(modifier = modifier) {
        OnlineWatchFaceDetailsContent(
            watchFaceState = watchFaceState,
            watchFaceName = watchFace.name,
            watchFaceImgUrl = watchFace.iconUrl ?: "",
            watchFaceLabels = watchFace.labels,
            watchFaceNotes = watchFaceNotes,
            showInstallButton = showInstallButton,
            showSetAsCurrentWatchfaceButton = showSetAsCurrentWatchfaceButton,
            enableInstallButton = enableInstallButton,
            enableUninstallButton = enableUninstallButton,
            showWatchFaceStatusButton = showWatchFaceStatusButton,
            onInstallClick = onInstallClick,
            onSetAsCurrentWatchfaceClick = onSetAsCurrentWatchfaceClick,
            onUninstallClick = onUninstallClick,
            onNoteAction = onNoteAction,
            modifier = Modifier.fillMaxSize()
        )

        WatchFaceAndNetworkNotificationStateComposable(
            state = watchFaceAndNetworkNotificationState,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .fillMaxWidth()
                .padding(
                    top = MaterialTheme.spacing.medium,
                    start = MaterialTheme.spacing.large,
                    end = MaterialTheme.spacing.large,
                ),
            onCustomToastActionClick = onCustomToastActionClick,
            watchSyncingMessage = stringResource(com.stt.android.R.string.sport_mode_watch_syncing),
            watchBusyMessage = stringResource(com.stt.android.R.string.suunto_plus_sync_watch_busy),
            noInternetConnectionMessage = stringResource(com.stt.android.R.string.no_internet_connection_error),
            watchDisconnectedMessage = stringResource(com.stt.android.R.string.sport_mode_watch_disconnected)
        )
    }
}

@Composable
private fun OnlineWatchFaceDetailsContent(
    watchFaceState: WatchFaceStatus,
    watchFaceName: String,
    watchFaceImgUrl: String,
    watchFaceLabels: List<String>?,
    watchFaceNotes: ImmutableList<WatchFaceNote>,
    showInstallButton: Boolean,
    showSetAsCurrentWatchfaceButton: Boolean,
    enableInstallButton: Boolean,
    enableUninstallButton: Boolean,
    showWatchFaceStatusButton: Boolean,
    onInstallClick: () -> Unit,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    onUninstallClick: () -> Unit,
    onNoteAction: (WatchFaceNote.NoteButtonAction) -> Unit,
    modifier: Modifier = Modifier,
) {
    ContentCenteringColumn(
        modifier = modifier.verticalScroll(rememberScrollState())
    ) {
        WatchFacePreviewImage(
            watchFaceImgUrl = watchFaceImgUrl
        )

        ConstraintLayout(
            modifier = Modifier
                .fillMaxWidth()
                .padding(MaterialTheme.spacing.medium)
        ) {
            val (title, labels) = createRefs()

            Text(
                text = watchFaceName,
                style = MaterialTheme.typography.bodyMegaBold,
                modifier = Modifier.constrainAs(title) {
                    top.linkTo(parent.top)
                    start.linkTo(parent.start)
                }
            )

            WatchFaceLabels(
                labels = watchFaceLabels,
                modifier = Modifier.constrainAs(labels) {
                    top.linkTo(title.bottom, 16.dp, 16.dp)
                    start.linkTo(title.start)
                }
            )
        }

        WatchFaceDetailNotes(
            watchFaceNotes = watchFaceNotes,
            onNoteAction = onNoteAction,
            modifier = Modifier.padding(horizontal = MaterialTheme.spacing.medium)
        )

        Spacer(modifier = Modifier.weight(1f))

        if (showWatchFaceStatusButton) {
            WatchFaceStates(
                watchFaceState = watchFaceState,
                modifier = Modifier.padding(bottom = MaterialTheme.spacing.xxxxlarge)
            )
        }

        WatchFaceBottomButtons(
            showInstallButton = showInstallButton,
            showSetAsCurrentWatchfaceButton = showSetAsCurrentWatchfaceButton,
            enableInstallButton = enableInstallButton,
            enableUninstallButton = enableUninstallButton,
            onInstallClick = onInstallClick,
            onSetAsCurrentWatchfaceClick = onSetAsCurrentWatchfaceClick,
            onRemoveFromWatch = onUninstallClick
        )
    }
}

@Composable
private fun WatchFacePreviewImage(
    watchFaceImgUrl: String,
    modifier: Modifier = Modifier,
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .fillMaxWidth()
            .background(MaterialTheme.colorScheme.nearWhite)
            .padding(vertical = 40.dp),
    ) {
        AsyncImage(
            model = ImageRequest.Builder(LocalContext.current)
                .data(watchFaceImgUrl)
                .crossfade(true)
                .build(),
            contentDescription = null,
            modifier = Modifier.size(174.dp),
        )
    }
}

@Composable
private fun WatchFaceLabels(
    labels: List<String>?,
    modifier: Modifier = Modifier
) {
    if (labels.isNullOrEmpty()) return
    FlowRow(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
        verticalArrangement = Arrangement.spacedBy(MaterialTheme.spacing.small),
    ) {
        for (label in labels) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .clip(RoundedCornerShape(16.dp))
                    .background(MaterialTheme.colorScheme.primary)
                    .padding(horizontal = 12.dp, vertical = 4.dp),
            ) {
                Text(
                    text = label,
                    style = MaterialTheme.typography.bodyMedium,
                    color = MaterialTheme.colorScheme.onSurface
                )
            }
        }
    }
}

@Composable
private fun WatchFaceBottomButtons(
    showInstallButton: Boolean,
    showSetAsCurrentWatchfaceButton: Boolean,
    enableInstallButton: Boolean,
    enableUninstallButton: Boolean,
    onInstallClick: () -> Unit,
    onSetAsCurrentWatchfaceClick: () -> Unit,
    onRemoveFromWatch: () -> Unit,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.fillMaxWidth()
    ) {
        if (showSetAsCurrentWatchfaceButton) {
            PrimaryButton(
                text = stringResource(id = com.stt.android.R.string.suunto_plus_set_as_current_watch_face),
                onClick = onSetAsCurrentWatchfaceClick,
                enabled = enableUninstallButton,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        }

        if (showInstallButton) {
            PrimaryButton(
                text = stringResource(id = R.string.suunto_plus_floating_action_button_install_on_watch),
                onClick = onInstallClick,
                enabled = enableInstallButton,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(MaterialTheme.spacing.medium)
            )
        } else {
            TextButton(
                onClick = onRemoveFromWatch,
                enabled = enableUninstallButton,
                colors = ButtonDefaults.textButtonColors(
                    contentColor = MaterialTheme.colorScheme.error,
                    disabledContentColor = MaterialTheme.colorScheme.error.copy(alpha = 0.6f)
                ),
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(bottom = MaterialTheme.spacing.medium)
            ) {
                Text(
                    text = "uninstall from watch".uppercase(),
                    style = MaterialTheme.typography.bodyBold
                )
            }
        }
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 360, heightDp = 720)
@Composable
private fun OnlineWatchFaceDetailsContentPreview() {
    M3AppTheme {
        OnlineWatchFaceDetailsContent(
            watchFaceState = WatchFaceStatus.UNKNOWN,
            watchFaceNotes = persistentListOf(),
            watchFaceName = "Watch face name",
            watchFaceImgUrl = "",
            watchFaceLabels = listOf("Label 1", "Label 2", "Label 3"),
            showInstallButton = true,
            showSetAsCurrentWatchfaceButton = false,
            enableInstallButton = true,
            enableUninstallButton = false,
            showWatchFaceStatusButton = false,
            onInstallClick = {},
            onSetAsCurrentWatchfaceClick = {},
            onUninstallClick = {},
            onNoteAction = {},
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 360, heightDp = 720)
@Composable
private fun OnlineWatchFaceDetailsSetAsCurrentPreview() {
    M3AppTheme {
        OnlineWatchFaceDetailsContent(
            watchFaceState = WatchFaceStatus.IN_WATCH,
            watchFaceName = "Watch face name",
            watchFaceNotes = persistentListOf(),
            watchFaceImgUrl = "",
            watchFaceLabels = listOf("Label 1", "Label 2", "Label 3"),
            showInstallButton = false,
            showSetAsCurrentWatchfaceButton = true,
            enableInstallButton = false,
            enableUninstallButton = true,
            showWatchFaceStatusButton = false,
            onInstallClick = {},
            onSetAsCurrentWatchfaceClick = {},
            onUninstallClick = {},
            onNoteAction = {},
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 360, heightDp = 720)
@Composable
private fun OnlineWatchFaceDetailsReadLimitPreview() {
    M3AppTheme {
        OnlineWatchFaceDetailsContent(
            watchFaceState = WatchFaceStatus.IN_WATCH,
            watchFaceName = "Watch face name",
            watchFaceNotes = persistentListOf(WatchFaceNote.MaxLimitReachedNote),
            watchFaceImgUrl = "",
            watchFaceLabels = listOf("Label 1", "Label 2", "Label 3"),
            showInstallButton = true,
            showSetAsCurrentWatchfaceButton = false,
            enableInstallButton = false,
            enableUninstallButton = false,
            showWatchFaceStatusButton = false,
            onInstallClick = {},
            onSetAsCurrentWatchfaceClick = {},
            onUninstallClick = {},
            onNoteAction = {},
        )
    }
}

@Preview(backgroundColor = 0xFFFFFF, showBackground = true, widthDp = 360, heightDp = 720)
@Composable
private fun OnlineWatchFaceDetailsInCompatiblePreview() {
    M3AppTheme {
        OnlineWatchFaceDetailsContent(
            watchFaceState = WatchFaceStatus.IN_WATCH,
            watchFaceName = "Watch face name",
            watchFaceNotes = persistentListOf(WatchFaceNote.InCompatible),
            watchFaceImgUrl = "",
            watchFaceLabels = listOf("Label 1", "Label 2", "Label 3"),
            showInstallButton = false,
            showSetAsCurrentWatchfaceButton = false,
            enableInstallButton = false,
            enableUninstallButton = false,
            showWatchFaceStatusButton = false,
            onInstallClick = {},
            onSetAsCurrentWatchfaceClick = {},
            onUninstallClick = {},
            onNoteAction = {},
        )
    }
}
