package com.stt.android.device.domain

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.M3AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.M3ErrorToast
import com.stt.android.compose.widgets.M3ProgressToast
import com.stt.android.compose.widgets.M3Toast
import androidx.compose.material3.MaterialTheme as M3Theme
import androidx.compose.material3.Text as M3Text
import androidx.compose.material3.TextButton as M3TextButton

@Composable
fun WatchFaceAndNetworkNotificationStateComposable(
    state: WatchFaceAndNetworkNotificationState,
    modifier: Modifier = Modifier,
    onCustomToastActionClick: () -> Unit = {},
    watchSyncingMessage: String? = null,
    watchBusyMessage: String? = null,
    watchDisconnectedMessage: String? = null,
    noGuideSupportMessage: String? = null,
    noInternetConnectionMessage: String? = null,
) {
    // The ordering of this when statement is important in case of multiple messages
    // are triggered simultaneously. Most important messages are checked first.
    val targetState = when {
        state.customToastMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.ToastWithPrimaryBackgroundColor(
                message = state.customToastMessage,
                actionText = state.customToastActionText
            )

        state.watchSyncing && watchSyncingMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.ProgressToast(watchSyncingMessage)

        state.watchBusy && watchBusyMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.Toast(watchBusyMessage)

        state.watchDisconnected && watchDisconnectedMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.Toast(watchDisconnectedMessage)

        state.watchPaired && state.isSuuntoRunWatchFaceSupported == false && noGuideSupportMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.ErrorToast(noGuideSupportMessage)

        !state.internetAvailable && noInternetConnectionMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.Toast(noInternetConnectionMessage)

        state.customErrorMessage != null ->
            WatchFaceAndNetworkNotificationTargetState.ErrorToast(state.customErrorMessage)

        else ->
            WatchFaceAndNetworkNotificationTargetState.None
    }

    WatchFaceAndNetworkNotificationStateContent(
        targetState = targetState,
        modifier = modifier,
        onCustomToastActionClick = onCustomToastActionClick,
    )
}

@Composable
private fun WatchFaceAndNetworkNotificationStateContent(
    targetState: WatchFaceAndNetworkNotificationTargetState,
    modifier: Modifier = Modifier,
    onCustomToastActionClick: () -> Unit = {},
) {
    Crossfade(
        targetState = targetState,
        modifier = modifier
    ) {
        val toastModifier = Modifier.fillMaxWidth()

        when (it) {
            is WatchFaceAndNetworkNotificationTargetState.ToastWithPrimaryBackgroundColor -> M3Toast(
                backgroundColor = M3Theme.colorScheme.primary,
                contentColor = M3Theme.colorScheme.onPrimary,
                modifier = toastModifier
            ) {
                M3Text(
                    text = it.message,
                    modifier = Modifier
                        .padding(MaterialTheme.spacing.small)
                        .then(if (it.actionText != null) Modifier.weight(1f) else Modifier)
                )

                if (it.actionText != null) {
                    M3TextButton(onClick = onCustomToastActionClick) {
                        M3Text(
                            text = it.actionText,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.padding(MaterialTheme.spacing.small),
                            color = M3Theme.colorScheme.onPrimary
                        )
                    }
                }
            }

            is WatchFaceAndNetworkNotificationTargetState.Toast -> M3Toast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchFaceAndNetworkNotificationTargetState.ProgressToast -> M3ProgressToast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchFaceAndNetworkNotificationTargetState.ErrorToast -> M3ErrorToast(
                text = it.message,
                modifier = toastModifier
            )

            is WatchFaceAndNetworkNotificationTargetState.None -> {}
        }
    }
}

sealed class WatchFaceAndNetworkNotificationTargetState {
    data class Toast(val message: String) : WatchFaceAndNetworkNotificationTargetState()
    data class ProgressToast(val message: String) : WatchFaceAndNetworkNotificationTargetState()
    data class ToastWithPrimaryBackgroundColor(
        val message: String,
        val actionText: String?
    ) : WatchFaceAndNetworkNotificationTargetState()
    data class ErrorToast(val message: String) : WatchFaceAndNetworkNotificationTargetState()
    object None : WatchFaceAndNetworkNotificationTargetState()
}

@Preview
@Composable
private fun CustomToastMessagePreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(
                customToastMessage = "watch face sync"
            )
        )
    }
}

@Preview
@Composable
private fun CustomToastMessageWithActionPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(
                customToastMessage = "watch face will installed during next sync",
                customToastActionText = "View",
            ),
            onCustomToastActionClick = {}
        )
    }
}

@Preview
@Composable
private fun WatchSyncNotificationPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(watchSyncing = true),
            watchSyncingMessage = "Watch is syncing"
        )
    }
}

@Preview
@Composable
private fun WatchBusyNotificationPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(watchBusy = true),
            watchBusyMessage = "Watch is busy. Modifications can be done when your watch is on the main screen."
        )
    }
}

@Preview
@Composable
private fun WatchDisconnectedNotificationPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(watchDisconnected = true),
            watchDisconnectedMessage = "Watch is not connected"
        )
    }
}

@Preview
@Composable
private fun WatchNotSupportedNotificationPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT,
            noGuideSupportMessage = "Watch not compatible with SuuntoPlus™ guides to sync workouts"
        )
    }
}

@Preview
@Composable
private fun NoInternetNotificationPreview() {
    M3AppTheme {
        WatchFaceAndNetworkNotificationStateComposable(
            state = WatchFaceAndNetworkNotificationState.DEFAULT.copy(internetAvailable = false),
            noInternetConnectionMessage = "No internet connection"
        )
    }
}
