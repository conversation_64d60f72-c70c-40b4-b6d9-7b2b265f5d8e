package com.stt.android.device.watchface.library

import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.nearWhite
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ConfirmationDialog
import com.stt.android.compose.widgets.M3ProgressToast
import com.stt.android.compose.widgets.M3Toast
import com.stt.android.data.source.local.watchface.WatchFaceSyncState
import com.stt.android.device.R
import com.stt.android.device.domain.watchface.WatchFace
import com.stt.android.device.domain.watchface.WatchFaceWithStatus
import com.stt.android.device.suuntoplusfeature.listitems.SyncNowButton
import com.stt.android.device.watchface.MyWatchFaceViewModel
import com.stt.android.device.watchface.OnlineWatchFaceTopBar
import com.stt.android.device.watchface.PullToRemoteSyncWatchFaceViewModel
import com.stt.android.device.watchface.datasource.WatchFacesMaxUsageReached
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.toImmutableList
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@Composable
fun MyWatchFaceLibraryScreen(
    viewModel: MyWatchFaceViewModel,
    pullToSyncViewModel: PullToRemoteSyncWatchFaceViewModel,
    onNavigateUp: () -> Unit,
    modifier: Modifier = Modifier,
) {
    val refreshing by pullToSyncViewModel.refreshing.collectAsState()

    Scaffold(
        topBar = {
            OnlineWatchFaceTopBar(
                title = stringResource(R.string.watch_face_in_my_watch_faces_label),
                onUpPressed = onNavigateUp,
            )
        },
        modifier = modifier,
    ) { internalPadding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(MaterialTheme.colorScheme.nearWhite)
                .padding(internalPadding)
        ) {
            when (val viewState = viewModel.viewState.collectAsState().value) {
                is MyWatchFaceViewModel.ViewState.Loading -> {
                    MyWatchFaceListLoading()
                }

                is MyWatchFaceViewModel.ViewState.Loaded -> {
                    Box {
                        MyWatchFaceListLoaded(
                            watchFaces = viewState.data.watchFaces.toImmutableList(),
                            canEnableMoreFeatures = viewState.data.canEnableMoreFeatures,
                            watchFacesOnWatchCount = viewState.data.watchFacesOnWatchCount,
                            watchFacesOnWatchCountMax = viewState.data.watchFacesOnWatchCountMax,
                            onWatchFaceCLick = {},
                            enableInstallActions = !viewState.data.watchBusy && viewState.data.syncState == WatchFaceSyncState.IDLE,
                            syncOngoing = viewState.data.syncState.isSyncOngoing,
                            onInstallWatchFace = viewModel::addToWatch,
                            onUninstallWatchFace = viewModel::removeFromWatch,
                            refreshing = refreshing,
                            enablePullToRefresh = viewState.data.syncState == WatchFaceSyncState.IDLE,
                            onRefresh = {
                                viewModel.resetSyncRequiredFlag()
                                pullToSyncViewModel.syncRemote()
                            },
                            onWatchFaceStoreClick = {},
                            modifier = modifier,
                        )

                        Crossfade(
                            targetState = viewState.data.notificationState,
                            modifier = Modifier
                                .padding(MaterialTheme.spacing.medium)
                                .widthIn(max = dimensionResource(id = CR.dimen.dialog_max_width))
                                .fillMaxWidth()
                        ) {
                            Box(
                                contentAlignment = Alignment.TopCenter,
                                modifier = Modifier.fillMaxWidth()
                            ) {
                                when (it) {
                                    MyWatchFaceViewModel.Notification.SYNCING -> M3ProgressToast(
                                        text = stringResource(id = BaseR.string.sport_mode_watch_syncing)
                                    )

                                    MyWatchFaceViewModel.Notification.BUSY -> M3Toast(
                                        text = stringResource(id = BaseR.string.suunto_plus_sync_watch_busy)
                                    )

                                    MyWatchFaceViewModel.Notification.DISCONNECTED -> M3Toast(
                                        text = stringResource(id = BaseR.string.sport_mode_watch_disconnected)
                                    )

                                    MyWatchFaceViewModel.Notification.SYNC_NOW_BUTTON -> SyncNowButton(
                                        onClick = {
                                            viewModel.syncWithRemote(alwaysTriggerWatchSync = true)
                                        }
                                    )

                                    MyWatchFaceViewModel.Notification.WATCH_FULL ->
                                        WatchFacesMaxUsageReached(stringResource(R.string.watch_face_max_usage_reached))

                                    MyWatchFaceViewModel.Notification.NONE -> {}
                                }
                            }
                        }
                    }
                }

                is MyWatchFaceViewModel.ViewState.Error -> {}
            }
        }
    }
}

@Composable
internal fun MyWatchFaceListLoading(
    modifier: Modifier = Modifier,
) {
    Box(modifier = modifier.fillMaxSize()) {
        CircularProgressIndicator(
            modifier = Modifier
                .align(Alignment.Center)
                .size(48.dp),
            color = MaterialTheme.colorScheme.primary,
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
internal fun MyWatchFaceListLoaded(
    watchFaces: ImmutableList<WatchFaceWithStatus>,
    refreshing: Boolean,
    syncOngoing: Boolean,
    enablePullToRefresh: Boolean,
    canEnableMoreFeatures: Boolean,
    watchFacesOnWatchCount: Int?,
    watchFacesOnWatchCountMax: Int?,
    enableInstallActions: Boolean,
    onInstallWatchFace: (WatchFace) -> Unit,
    onUninstallWatchFace: (WatchFace) -> Unit,
    onRefresh: () -> Unit,
    onWatchFaceCLick: (WatchFace) -> Unit,
    onWatchFaceStoreClick: () -> Unit,
    modifier: Modifier = Modifier,
) {
    var showUninstallConfirmDialog by remember { mutableStateOf(false) }
    var uninstallWatchFace by remember { mutableStateOf<WatchFace?>(null) }

    val pullRefreshState = rememberPullRefreshState(
        refreshing = refreshing,
        onRefresh = onRefresh
    )

    if (showUninstallConfirmDialog) {
        ConfirmationDialog(
            text = stringResource(R.string.watch_face_remove_from_watch_confirm_content),
            cancelButtonText = stringResource(id = BaseR.string.cancel),
            confirmButtonText = stringResource(id = BaseR.string.delete),
            onDismissRequest = {
                uninstallWatchFace = null
                showUninstallConfirmDialog = false
            },
            onConfirm = {
                showUninstallConfirmDialog = false
                uninstallWatchFace?.let { onUninstallWatchFace(it) }
            },
            useDestructiveColorForConfirm = true
        )
    }

    Box(modifier = Modifier.pullRefresh(pullRefreshState, enablePullToRefresh)) {
        LazyColumn(
            modifier = modifier
                .narrowContent()
                .fillMaxHeight()
        ) {
            if (watchFacesOnWatchCount != null && watchFacesOnWatchCountMax != null) {
                watchFacesUsageMeter(
                    usage = watchFacesOnWatchCount,
                    maxUsage = watchFacesOnWatchCountMax,
                    syncOngoing = syncOngoing
                )
            }

            watchFaceList(
                watchFaces = watchFaces,
                canEnableMoreFeatures = canEnableMoreFeatures,
                enableInstallActions = enableInstallActions,
                onItemCLick = onWatchFaceCLick,
                onInstallClick = onInstallWatchFace,
                onUninstallClick = {
                    uninstallWatchFace = it
                    showUninstallConfirmDialog = true
                },
            )

            findNewWatchFaces(
                onWatchFaceStoreClick = onWatchFaceStoreClick
            )
        }

        PullRefreshIndicator(
            refreshing = refreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}
