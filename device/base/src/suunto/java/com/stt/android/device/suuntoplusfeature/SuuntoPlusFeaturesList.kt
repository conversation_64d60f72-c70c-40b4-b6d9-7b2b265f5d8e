package com.stt.android.device.suuntoplusfeature

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.MaterialTheme
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.confirmation
import com.stt.android.compose.theme.dividerColor
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.util.rememberEventThrottler
import com.stt.android.device.R
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeatureWithStatus
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusPluginStatus
import com.stt.android.device.suuntoplusfeature.details.ConfirmUninstallDialog
import com.stt.android.device.suuntoplusfeature.listitems.FeatureListEmptyState
import com.stt.android.device.suuntoplusfeature.listitems.FeatureListItem
import com.stt.android.device.suuntoplusfeature.listitems.StoreLinkItem
import com.stt.android.device.suuntoplusfeature.listitems.WatchfaceListEmptyState
import com.stt.android.suuntoplus.SuuntoPlusItemLabel
import com.stt.android.suuntoplus.ui.WatchStatus
import com.stt.android.suuntoplus.ui.list.SuuntoPlusListClickableTextItem
import com.stt.android.suuntoplus.ui.list.SuuntoPlusListSectionHeader
import com.stt.android.ui.utils.TextFormatter
import kotlinx.collections.immutable.ImmutableList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import com.stt.android.R as BaseR

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun SuuntoPlusFeaturesList(
    features: ImmutableList<SuuntoPlusFeatureWithStatus>,
    currentTimeMillis: Long,
    refreshing: Boolean,
    syncOngoing: Boolean,
    enablePullToRefresh: Boolean,
    canEnableMoreFeatures: Boolean,
    isShowConfirmDialog: Boolean,
    featuresOnWatchCount: Int?,
    featuresOnWatchCountMax: Int?,
    enableInstallActions: Boolean,
    onInstallFeature: (SuuntoPlusFeature) -> Unit,
    onUninstallFeature: (SuuntoPlusFeature) -> Unit,
    onShowUninstallConfirmDialog: (SuuntoPlusFeature) -> Unit,
    onConfirmUninstallFeature: () -> Unit,
    onConfirmCancel: () -> Unit,
    onFeatureClick: (SuuntoPlusFeature) -> Unit,
    onTutorialClick: () -> Unit,
    onPlusStoreLinkClick: () -> Unit,
    onRefresh: () -> Unit,
    isWatchfaceList: Boolean,
    currentWatchfaceId: String?,
    isWatchfaceSupported: Boolean,
    isRunDevice: Boolean,
    modifier: Modifier = Modifier
) {
    val clickThrottler = rememberEventThrottler()

    val pullRefreshState = rememberPullRefreshState(
        refreshing = refreshing,
        onRefresh = onRefresh
    )

    if (isShowConfirmDialog) {
        ConfirmUninstallDialog(
            onUninstallClick = {
                onConfirmUninstallFeature()
            },
            onCancelClick = {
                onConfirmCancel()
            },
            isWatchface = true
        )
    }

    Box(modifier = modifier.pullRefresh(pullRefreshState, enablePullToRefresh)) {
        LazyColumn {
            item(key = "How to get started and counter") {
                if (!isWatchfaceList) {
                    SuuntoPlusListClickableTextItem(
                        text = stringResource(id = BaseR.string.suunto_plus_features_list_tutorial),
                        onClick = { clickThrottler.processEvent(onTutorialClick) },
                    )
                }
                Divider(color = MaterialTheme.colors.dividerColor)

                if (featuresOnWatchCount != null && featuresOnWatchCountMax != null) {
                    SuuntoPlusFeaturesUsageMeter(
                        usage = featuresOnWatchCount,
                        maxUsage = featuresOnWatchCountMax,
                        syncOngoing = syncOngoing,
                        isWatchfaceList = isWatchfaceList
                    )
                }

                Divider(color = MaterialTheme.colors.dividerColor)
            }

            if (features.isEmpty()) {
                item(key = "Empty state") {
                    val emptyModifier = Modifier
                        .fillMaxWidth()
                        .padding(vertical = MaterialTheme.spacing.large)
                    if (isWatchfaceList) {
                        WatchfaceListEmptyState(
                            modifier = emptyModifier
                        )
                    } else {
                        FeatureListEmptyState(
                            modifier = emptyModifier
                        )
                    }
                }
            }

            items(
                items = features,
                key = { (feature, _) -> feature.id }
            ) { featureWithStatus ->
                val (feature, status) = featureWithStatus
                val isCurrentWatchface = feature.isWatchface() && currentWatchfaceId != null && feature.id == currentWatchfaceId

                val buttonText: String?
                val buttonTextColor: Color?
                val buttonEnabled: Boolean
                val onButtonClick: () -> Unit

                if (feature.hasExpired(currentTimeMillis) || status == SuuntoPlusPluginStatus.NOT_SUPPORTED) {
                    buttonText = null
                    buttonTextColor = null
                    buttonEnabled = false
                    onButtonClick = {}
                } else if (feature.enabled) {
                    buttonText =
                        stringResource(id = R.string.suunto_plus_text_button_uninstall_from_watch_short)
                    buttonTextColor = MaterialTheme.colors.error
                    buttonEnabled = enableInstallActions
                    onButtonClick = {
                        if (feature.isWatchface()) {
                            clickThrottler.processEvent {
                                onShowUninstallConfirmDialog(feature)
                            }
                        } else {
                            clickThrottler.processEvent {
                                onUninstallFeature(feature)
                            }
                        }
                    }
                } else {
                    buttonText =
                        stringResource(id = R.string.suunto_plus_text_button_install_on_watch_short)
                    buttonTextColor = MaterialTheme.colors.primary
                    buttonEnabled = enableInstallActions && canEnableMoreFeatures
                    onButtonClick = { clickThrottler.processEvent { onInstallFeature(feature) } }
                }

                val expired = feature.hasExpired(currentTimeMillis)

                FeatureListItem(
                    title = feature.name,
                    shortDescription = feature.shortDescription ?: feature.description,
                    watchPreviewImageUrl = feature.iconUrl,
                    buttonText = buttonText,
                    buttonTextColor = buttonTextColor,
                    enableButton = buttonEnabled,
                    dimmed = expired,
                    watchStatus = featureWithStatus.getBadgeWatchStatus(currentTimeMillis),
                    expirationDate = if (expired && feature.expirationTimeMillis != null) {
                        TextFormatter.formatDate(
                            LocalContext.current,
                            feature.expirationTimeMillis,
                            true
                        )
                    } else {
                        null
                    },
                    onButtonClick = onButtonClick,
                    onClick = {
                        if (!expired) {
                            clickThrottler.processEvent { onFeatureClick(feature) }
                        }
                    },
                    itemLabels = if (isCurrentWatchface) {
                        buildList {
                            add(
                                SuuntoPlusItemLabel(
                                    text = stringResource(id = R.string.suunto_plus_store_watch_faces_in_use_label),
                                    color = MaterialTheme.colors.confirmation
                                )
                            )
                        }.toPersistentList()
                    } else {
                        null
                    }
                )

                Divider(color = MaterialTheme.colors.dividerColor)
            }

            item(key = "Find new sports apps") {
                SuuntoPlusListSectionHeader(
                    text = stringResource(
                        id = if (isWatchfaceList) {
                            R.string.suunto_plus_guides_looking_for_new_watch_faces
                        } else {
                            R.string.suunto_plus_guides_looking_for_new_sports_apps
                        }
                    ),
                    topPadding = MaterialTheme.spacing.large,
                )

                StoreLinkItem(
                    onClick = { clickThrottler.processEvent(onPlusStoreLinkClick) },
                    isWatchfaceSupported = isWatchfaceSupported,
                    isRunDevice = isRunDevice,
                    modifier = Modifier
                        .padding(
                            start = MaterialTheme.spacing.small,
                            end = MaterialTheme.spacing.small,
                            bottom = MaterialTheme.spacing.xlarge,
                        ),
                )
            }
        }

        PullRefreshIndicator(
            refreshing = refreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@Preview
@Composable
private fun SuuntoPlusFeaturesListPreview() {
    AppTheme {
        SuuntoPlusFeaturesList(
            features = persistentListOf(
                SuuntoPlusFeatureWithStatus(
                    SuuntoPlusFeature(
                        id = "zzmaes01",
                        pluginId = "zzmaesen",
                        modifiedMillis = 1622533643000L,
                        name = "Marathon estimator",
                        owner = "Suunto",
                        description = "Estimated race time",
                        richDescription = null,
                        url = null,
                        iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzmaes01.png",
                        bannerUrl = null,
                        ownerLogoUrl = null,
                        enabled = false,
                        expirationTimeMillis = null,
                        shortDescription = "Estimated race time",
                        manifestJson = null,
                        localizedRichText = null,
                        localizedRichTextAutomatically = false,
                        type = null,
                        rating = null,
                    ),
                    SuuntoPlusPluginStatus.UNKNOWN,
                ),
                SuuntoPlusFeatureWithStatus(
                    SuuntoPlusFeature(
                        id = "zztrph01",
                        pluginId = "zztrphen",
                        modifiedMillis = 1622533643000L,
                        name = "TrainingPeaks",
                        owner = "Suunto",
                        description = "TrainingPeaks with Heart Rate: Heart Rate Training Stress Score® (hrTSS).",
                        richDescription = null,
                        url = null,
                        iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zztrph01.png",
                        bannerUrl = null,
                        ownerLogoUrl = null,
                        enabled = true,
                        expirationTimeMillis = null,
                        shortDescription = "Heart Rate",
                        manifestJson = null,
                        localizedRichText = null,
                        localizedRichTextAutomatically = false,
                        type = null,
                        rating = null,
                    ),
                    SuuntoPlusPluginStatus.IN_WATCH,
                ),
                SuuntoPlusFeatureWithStatus(
                    SuuntoPlusFeature(
                        id = "zzulra01",
                        pluginId = "zzulraen",
                        modifiedMillis = 1622533643000L,
                        name = "Ultra race time",
                        owner = "Suunto",
                        description = "Racetime of current pace",
                        richDescription = null,
                        url = null,
                        iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/default.png",
                        bannerUrl = null,
                        ownerLogoUrl = null,
                        enabled = true,
                        expirationTimeMillis = null,
                        shortDescription = "Racetime of current pace",
                        manifestJson = null,
                        localizedRichText = null,
                        localizedRichTextAutomatically = false,
                        type = null,
                        rating = null,
                    ),
                    SuuntoPlusPluginStatus.IN_WATCH
                ),
                SuuntoPlusFeatureWithStatus(
                    SuuntoPlusFeature(
                        id = "zzweth01",
                        pluginId = "zzwethen",
                        name = "Weather",
                        shortDescription = "Insights",
                        description = "Follow context-based weather insights and warnings during your outdoor activity. Keep alert with storm alarms, measure water temperature or notice if your hike is going past the sunset.",
                        iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzweth01.png",
                        modifiedMillis = 1672650000000L,
                        owner = "Suunto",
                        url = null,
                        bannerUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-banners/zzweth01.jpg",
                        enabled = false,
                        expirationTimeMillis = null,
                        manifestJson = null,
                        ownerLogoUrl = null,
                        richDescription = null,
                        localizedRichText = null,
                        localizedRichTextAutomatically = false,
                        type = null,
                        rating = null,
                    ),
                    SuuntoPlusPluginStatus.NOT_SUPPORTED
                ),
                SuuntoPlusFeatureWithStatus(
                    SuuntoPlusFeature(
                        id = "zzwifl01",
                        pluginId = "zzwiflen",
                        modifiedMillis = 1622533643000L,
                        name = "World Run",
                        owner = "Suunto",
                        description = "...",
                        richDescription = null,
                        url = null,
                        iconUrl = "https://suuntopluspluginsdev.blob.core.windows.net/feature-icons/zzwifl01.png",
                        bannerUrl = null,
                        ownerLogoUrl = null,
                        enabled = false,
                        expirationTimeMillis = 1640995200000L,
                        shortDescription = "World Run",
                        manifestJson = null,
                        localizedRichText = null,
                        localizedRichTextAutomatically = false,
                        type = null,
                        rating = null,
                    ),
                    SuuntoPlusPluginStatus.UNKNOWN
                )
            ),
            currentTimeMillis = 1668672570846L,
            enableInstallActions = true,
            syncOngoing = false,
            onInstallFeature = {},
            onUninstallFeature = {},
            onShowUninstallConfirmDialog = {},
            onConfirmUninstallFeature = {},
            onConfirmCancel = {},
            canEnableMoreFeatures = true,
            isShowConfirmDialog = false,
            featuresOnWatchCount = 1,
            featuresOnWatchCountMax = 15,
            onFeatureClick = {},
            onPlusStoreLinkClick = {},
            onTutorialClick = {},
            refreshing = false,
            enablePullToRefresh = true,
            onRefresh = {},
            modifier = Modifier.background(MaterialTheme.colors.background),
            isWatchfaceList = true,
            currentWatchfaceId = "",
            isWatchfaceSupported = true,
            isRunDevice = false
        )
    }
}

private fun SuuntoPlusFeatureWithStatus.getBadgeWatchStatus(currentTimeMillis: Long): WatchStatus? =
    when {
        feature.hasExpired(currentTimeMillis) -> WatchStatus.INCOMPATIBLE
        status == SuuntoPlusPluginStatus.NOT_SUPPORTED -> WatchStatus.INCOMPATIBLE
        status == SuuntoPlusPluginStatus.IN_WATCH -> WatchStatus.INSTALLED
        status == SuuntoPlusPluginStatus.WATCH_FULL -> WatchStatus.ERROR
        status == SuuntoPlusPluginStatus.INSTALLING -> WatchStatus.INSTALLING
        else -> null // Null means not installed (probably because we have not synced yet)
    }
