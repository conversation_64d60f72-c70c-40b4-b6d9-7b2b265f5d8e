package com.stt.android.device.watchface.navigation

import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavGraphBuilder
import androidx.navigation.compose.composable
import com.stt.android.device.watchface.library.MyWatchFaceLibraryScreen

internal fun NavGraphBuilder.watchFaceLibraryDestination(
    onNavigateUp: () -> Unit,
) {
    composable(WatchFaceLibraryRoute.WATCH_FACE_LIBRARY) {
        MyWatchFaceLibraryScreen(
            viewModel = hiltViewModel(),
            pullToSyncViewModel = hiltViewModel(),
            onNavigateUp = onNavigateUp,
        )
    }
}
