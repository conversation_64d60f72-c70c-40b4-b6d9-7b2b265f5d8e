package com.stt.android.device.domain.watchface

import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import com.stt.android.device.remote.watchface.WatchFaceRemoteDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AddWatchFaceToLibraryUseCase @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
    private val remoteDataSource: WatchFaceRemoteDataSource,
) {
    suspend fun addWatchFaceToWatch(
        id: String,
        watchCapabilities: String,
    ) = withContext(Dispatchers.IO) {
        val existingLocalFeature = localDataSource.findById(id)
        if (existingLocalFeature?.addToWatch == false) {
            // The watchFace already exists, let's just enable it
            localDataSource.updateAddToWatchEnabled(id, true)
            // update remote watch face addToWatch flag
            addWatchFaceToLibrary(id, watchCapabilities, true)
        } else {
            // Add feature using the Store API
            addWatchFaceToLibrary(id, watchCapabilities, true)
        }
    }

    suspend fun addWatchFaceToLibrary(
        id: String,
        watchCapabilities: String,
        addToWatch: Boolean
    ) {
        remoteDataSource.addToLibrary(
            id = id,
            watchFaceCapabilities = watchCapabilities,
            addToFavorite = true,
            addToWatch = addToWatch
        )
    }
}
