package com.stt.android.device.suuntoplusfeature

import androidx.compose.runtime.Immutable
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeatureWithStatus
import kotlinx.collections.immutable.ImmutableList

@Immutable
data class SuuntoPlusFeaturesListContainer(
    val features: ImmutableList<SuuntoPlusFeatureWithStatus>,
    val currentTimeMillis: Long,
    val syncState: SuuntoPlusSyncState,
    val refreshing: <PERSON><PERSON>an,
    val watchBusy: <PERSON><PERSON><PERSON>,
    val watchDisconnected: <PERSON><PERSON><PERSON>,
    val showSyncNowButton: <PERSON><PERSON><PERSON>,
    val showWatchFullNotification: <PERSON><PERSON>an,
    val canEnableMoreFeatures: Boolean,
    val featuresOnWatchCount: Int?,
    val featuresOnWatchCountMax: Int?,
    val currentWatchfaceId: String?,
    val isWatchfaceSupported: <PERSON><PERSON><PERSON>,
    val isRunDevice: <PERSON><PERSON><PERSON>,
)
