package com.stt.android.device.suuntoplusguide

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.activityViewModels
import androidx.fragment.app.viewModels
import androidx.navigation.NavDirections
import androidx.navigation.fragment.findNavController
import com.google.android.material.snackbar.Snackbar
import com.stt.android.analytics.AnalyticsPropertyValue.ConnectedServicesListSource.SUUNTO_PLUS_GUIDES_SELECTION_SCREEN
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.device.R
import com.stt.android.device.domain.WatchAndNetworkNotificationStateComposable
import com.stt.android.device.domain.WatchAndNetworkNotificationViewModel
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuide
import com.stt.android.device.domain.suuntoplusguide.SuuntoPlusGuideId
import com.stt.android.device.domain.suuntoplusguide.TrainingPlanId
import com.stt.android.device.suuntoplusfeature.PullToRemoteSyncSuuntoPlusViewModel
import com.stt.android.domain.connectedservices.ServiceMetadata
import com.stt.android.home.settings.connectedservices.ConnectedServicesActivity
import com.stt.android.watch.SuuntoPlusStoreNavigator
import com.stt.android.watch.WorkoutPlannerNavigator
import dagger.hilt.android.AndroidEntryPoint
import javax.inject.Inject
import com.stt.android.R as BaseR

@AndroidEntryPoint
class SuuntoPlusGuideListFragment : Fragment() {

    private val viewModel: SuuntoPlusGuideListViewModel by viewModels()
    private val pullToSyncViewModel: PullToRemoteSyncSuuntoPlusViewModel by viewModels()

    // View model for listening to watch and sync state
    private val watchAndNetworkNotificationViewModel: WatchAndNetworkNotificationViewModel by activityViewModels()

    @Inject
    lateinit var suuntoPlusStoreNavigator: SuuntoPlusStoreNavigator

    @Inject
    lateinit var plannerNavigator: WorkoutPlannerNavigator

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )

            setContent {
                AppTheme {
                    val refreshing by pullToSyncViewModel.refreshing.collectAsState()
                    val viewState by viewModel.viewState.observeAsState()
                    val watchAndNetworkState =
                        watchAndNetworkNotificationViewModel.watchAndNetworkNotificationState
                            .collectAsState()

                    val data = viewState?.data ?: return@AppTheme

                    Box(
                        contentAlignment = Alignment.TopCenter,
                        modifier = Modifier
                            .background(MaterialTheme.colors.background)
                            .narrowContent(),
                    ) {
                        SuuntoPlusGuideList(
                            inWatchGuides = data.inWatchGuides,
                            notInWatchGuides = data.notInWatchGuides,
                            notSupportedGuides = data.notSupportedGuides,
                            reconnectRecommendations = data.reconnectRecommendations,
                            onReconnectClick = ::reconnectPartner,
                            refreshing = refreshing,
                            enableActions = !watchAndNetworkState.value.watchSyncing,
                            enableGuides = true,
                            isSuuntoRun = viewModel.isRunDevice(),
                            onHowToClick = ::showHowTo,
                            onGuideDetailsClick = {
                                if (it.planId != null) {
                                    openTrainingPlanDetails(it)
                                } else {
                                    openGuideDetails(it)
                                }
                            },
                            onDeleteConfirmationShown = {},
                            onDeleteGuideConfirm = { guideId, isPlan ->
                                if (isPlan) {
                                    viewModel.deletePlan(TrainingPlanId(guideId))
                                } else {
                                    viewModel.deleteGuide(SuuntoPlusGuideId(guideId))
                                }

                                Snackbar.make(
                                    requireView(),
                                    if (isPlan) BaseR.string.suunto_plus_plan_was_deleted else BaseR.string.suunto_plus_guide_was_deleted,
                                    Snackbar.LENGTH_LONG
                                ).show()
                            },
                            onCreateWorkoutPlanClick = ::launchWorkoutPlanner,
                            onGuidesBySuuntoClick = ::showGuidesBySuuntoInStore,
                            onPartnerServicesClick = ::showPartnersInStore,
                            onPartnerServicesLearnMoreClick = ::launchLearnMoreAboutPartnersLink,
                            onRefresh = { pullToSyncViewModel.syncRemote() },
                        )

                        WatchAndNetworkNotificationStateComposable(
                            state = watchAndNetworkState.value,
                            modifier = Modifier
                                .align(Alignment.TopCenter)
                                .fillMaxWidth()
                                .padding(
                                    top = MaterialTheme.spacing.medium,
                                    start = MaterialTheme.spacing.large,
                                    end = MaterialTheme.spacing.large,
                                ),
                            watchBusyMessage = stringResource(BaseR.string.suunto_plus_sync_watch_busy),
                            watchDisconnectedMessage = stringResource(BaseR.string.sport_mode_watch_disconnected),
                            watchSyncingMessage = stringResource(BaseR.string.sport_mode_watch_syncing)
                        )
                    }
                }
            }
        }
    }

    @Deprecated(
        message = "Deprecated in Java",
        replaceWith = ReplaceWith(
            "inflater.inflate(R.menu.guide_list_menu, menu)",
            "com.stt.android.device.R"
        )
    )
    override fun onCreateOptionsMenu(menu: Menu, inflater: MenuInflater) {
        inflater.inflate(R.menu.guide_list_menu, menu)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setHasOptionsMenu(true)
    }

    private fun openGuideDetails(guide: SuuntoPlusGuide) {
        viewModel.sendBottomSheetOpenedEvent()

        navigateSafely(
            SuuntoPlusGuideListFragmentDirections.actionFromGuideListToDetails(
                guideId = guide.id.id,
                title = guide.name
            )
        )
    }

    private fun openTrainingPlanDetails(guide: SuuntoPlusGuide) {
        viewModel.sendBottomSheetOpenedEvent()

        navigateSafely(
            SuuntoPlusGuideListFragmentDirections.actionFromGuideListToPlanDetails(
                pluginId = guide.id.id,
                planId = guide.planId!!,
                title = guide.name
            )
        )
    }

    private fun showHowTo() {
        navigateSafely(
            SuuntoPlusGuideListFragmentDirections.actionFromGuideListFragmentToHowTo()
        )
    }

    private fun reconnectPartner(serviceMetadata: ServiceMetadata) {
        startActivity(
            ConnectedServicesActivity.newIntent(
                requireContext(),
                serviceMetadata = serviceMetadata,
                enableReconnectRecommendation = true,
                source = SUUNTO_PLUS_GUIDES_SELECTION_SCREEN,
            )
        )
    }

    private fun showGuidesBySuuntoInStore() {
        startActivity(
            suuntoPlusStoreNavigator.newSuuntoPlusStoreGuidesPageIntent(
                context = requireContext(),
                noBackStack = true
            )
        )
    }

    private fun showPartnersInStore() {
        startActivity(
            suuntoPlusStoreNavigator.newSuuntoPlusStorePartnersPageIntent(
                context = requireContext(),
                noBackStack = true
            )
        )
    }

    private fun launchLearnMoreAboutPartnersLink() {
        SuuntoPlusExternalLinks.launchPartnersLearnMoreLink(requireContext())
    }

    override fun onStart() {
        super.onStart()

        viewModel.sendSelectionScreenEvent()
    }

    override fun onResume() {
        super.onResume()
        viewModel.reloadIfNeeded()
    }

    override fun onPause() {
        super.onPause()

        viewModel.syncNowIfDelayedSyncRequested()
        viewModel.sendUserMadeChangesEventIfNeeded()
    }

    @Deprecated("Deprecated in Java")
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.open_workout_planner -> {
                launchWorkoutPlanner()
                return true
            }
        }
        return super.onOptionsItemSelected(item)
    }

    private fun launchWorkoutPlanner() {
        startActivity(
            plannerNavigator.newListWorkoutPlansIntent(
                context = requireContext(),
                analyticsSource = SUUNTO_PLUS_GUIDES_SELECTION_SCREEN
            )
        )
    }

    private fun navigateSafely(directions: NavDirections) = with(findNavController()) {
        if (currentDestination?.id == R.id.guideListFragment) {
            navigate(directions)
        }
    }
}
