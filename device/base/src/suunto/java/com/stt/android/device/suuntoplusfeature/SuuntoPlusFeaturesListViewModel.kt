package com.stt.android.device.suuntoplusfeature

import androidx.lifecycle.LiveData
import com.stt.android.common.coroutines.CoroutinesDispatchers
import com.stt.android.common.viewstate.LoadingStateViewModel
import com.stt.android.coroutines.combine
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.domain.GetCurrentWatchfaceIdUseCase
import com.stt.android.device.domain.GetDefaultWatchfaceIdUseCase
import com.stt.android.device.domain.IsSuuntoPlusWatchfaceSupportedUseCase
import com.stt.android.device.domain.suuntoplusfeature.ListFeaturesUseCase
import com.stt.android.device.domain.suuntoplusfeature.NumberOfEnabledFeaturesUseCase
import com.stt.android.device.domain.suuntoplusfeature.SetEnabledStateForFeatureUseCase
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeatureWithStatus
import com.stt.android.device.domain.suuntoplusguide.IsSuuntoPlusGuideSyncOngoingUseCase
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTrigger
import com.stt.android.device.remote.suuntoplusguide.DelayedSuuntoPlusGuideRemoteSyncTriggerImpl
import com.stt.android.device.suuntoplusguide.SuuntoPlusGuideAnalyticsUtils
import com.stt.android.device.watch.SuuntoPlusWatchStateListener
import com.stt.android.device.watch.SuuntoPlusWatchStateListenerImpl
import com.stt.android.domain.di.IoThread
import com.stt.android.domain.di.MainThread
import com.stt.android.domain.watch.IsWatchBusyUseCase
import com.stt.android.domain.watch.IsWatchConnectedUseCase
import com.stt.android.ui.utils.SingleLiveEvent
import com.stt.android.watch.IsRunDeviceUseCase
import com.stt.android.watch.background.SuuntoPlusGuideRemoteSyncJobLauncher
import dagger.hilt.android.lifecycle.HiltViewModel
import io.reactivex.Scheduler
import kotlinx.collections.immutable.toImmutableList
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import timber.log.Timber
import java.text.Collator
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class SuuntoPlusFeaturesListViewModel
@Inject constructor(
    private val listFeaturesUseCase: ListFeaturesUseCase,
    private val setEnabledStateForFeatureUseCase: SetEnabledStateForFeatureUseCase,
    remoteSyncJobLauncher: SuuntoPlusGuideRemoteSyncJobLauncher,
    private val suuntoPlusGuideAnalyticsUtils: SuuntoPlusGuideAnalyticsUtils,
    private val isSyncOngoingUseCase: IsSuuntoPlusGuideSyncOngoingUseCase,
    private val isWatchBusyUseCase: IsWatchBusyUseCase,
    private val isWatchConnectedUseCase: IsWatchConnectedUseCase,
    private val numberOfEnabledFeaturesUseCase: NumberOfEnabledFeaturesUseCase,
    @IoThread ioThread: Scheduler,
    @MainThread mainThread: Scheduler,
    coroutinesDispatchers: CoroutinesDispatchers,
    private val getCurrentWatchfaceIdUseCase: GetCurrentWatchfaceIdUseCase,
    private val isSuuntoPlusWatchfaceSupportedUseCase: IsSuuntoPlusWatchfaceSupportedUseCase,
    private val getDefaultWatchfaceIdUseCase: GetDefaultWatchfaceIdUseCase,
    private val isRunDeviceUseCase: IsRunDeviceUseCase
) : LoadingStateViewModel<SuuntoPlusFeaturesListContainer>(
    ioThread,
    mainThread,
    coroutinesDispatchers
),
    DelayedSuuntoPlusGuideRemoteSyncTrigger by DelayedSuuntoPlusGuideRemoteSyncTriggerImpl(
        remoteSyncJobLauncher
    ),
    SuuntoPlusWatchStateListener by SuuntoPlusWatchStateListenerImpl(
        isSyncOngoingUseCase,
        isWatchBusyUseCase
    ) {
    private var userMadeChanges = false
    private val collator = Collator.getInstance(Locale.getDefault())

    private val syncRequired = MutableStateFlow(false)

    private var loadDataJob: Job? = null

    val showUninstallConfirmDialog: LiveData<Boolean>
        get() = _showUninstallConfirmDialog
    private val _showUninstallConfirmDialog = SingleLiveEvent<Boolean>()

    private var currentUninstallFeature: SuuntoPlusFeature? = null

    private data class CombinedFeatureData(
        val featureContainer: ListFeaturesUseCase.FeatureContainer,
        val syncState: SuuntoPlusSyncState,
        val watchBusy: Boolean,
        val watchDisconnected: Boolean,
        val syncRequired: Boolean,
        val currentWatchfaceId: String?,
    ) {
        companion object {
            val EMPTY = CombinedFeatureData(
                featureContainer = ListFeaturesUseCase.FeatureContainer.EMPTY,
                syncState = SuuntoPlusSyncState.IDLE,
                watchBusy = false,
                watchDisconnected = false,
                syncRequired = false,
                currentWatchfaceId = null,
            )
        }
    }

    fun loadData(excludedWatchface: Boolean, onlyWatchface: Boolean) {
        notifyLoading()
        syncWithRemote()
        loadDataJob?.cancel()
        loadDataJob = launch {
            val isWatchfaceSupported = isSuuntoPlusWatchfaceSupportedUseCase()
            val defaultWatchfaceId =
                if (isWatchfaceSupported) getDefaultWatchfaceIdUseCase() else null

            combine(
                listFeaturesUseCase.listFeaturesAsFlow(),
                isSyncOngoingUseCase.getSyncStateFlow(),
                isWatchBusyUseCase(),
                isWatchConnectedUseCase.invoke(),
                syncRequired,
                getCurrentWatchfaceIdUseCase(),
                ::CombinedFeatureData
            )
                .catch {
                    Timber.w(it, "Failed to load Features list")
                    emit(CombinedFeatureData.EMPTY)
                }
                .onEach { (_, syncState, _, _) ->
                    if (syncState.isSyncOngoing) {
                        syncRequired.value = false
                    }
                }
                .collect { (featureContainer, syncState, watchBusy, watchConnected, syncRequired, currentWatchfaceId) ->
                    val (featuresOnWatchCount, featuresOnWatchCountMax) = numberOfEnabledFeaturesUseCase.numberOfEnabledAndMaxFeatures(onlyWatchface)

                    notifyDataLoaded(
                        SuuntoPlusFeaturesListContainer(
                            features = featureContainer.features
                                .filterByWatchfaceId(defaultWatchfaceId) { onlyWatchface && isWatchfaceSupported }
                                .sorted()
                                .plus(featureContainer.expiredAndNonCompatibleFeatures.sorted())
                                .filter {
                                    when {
                                        excludedWatchface -> !it.feature.isWatchface()
                                        onlyWatchface -> it.feature.isWatchface()
                                        else -> true
                                    }
                                }
                                .toImmutableList(),
                            currentTimeMillis = featureContainer.currentTimeMillis,
                            syncState = syncState,
                            watchBusy = watchBusy,
                            watchDisconnected = !watchConnected,
                            showSyncNowButton = syncRequired,
                            refreshing = syncState == SuuntoPlusSyncState.REMOTE_SYNC_ONGOING,
                            showWatchFullNotification = featureContainer.watchSupportsFeatures && !numberOfEnabledFeaturesUseCase.canOneMoreFeatureBeEnabled(onlyWatchface),
                            canEnableMoreFeatures = numberOfEnabledFeaturesUseCase.canOneMoreFeatureBeEnabled(onlyWatchface),
                            featuresOnWatchCount = featuresOnWatchCount,
                            featuresOnWatchCountMax = featuresOnWatchCountMax,
                            currentWatchfaceId = if (watchConnected) currentWatchfaceId else null,
                            isWatchfaceSupported = isWatchfaceSupported,
                            isRunDevice = isRunDeviceUseCase()
                        )
                    )
                }
        }
    }

    private fun List<SuuntoPlusFeatureWithStatus>.filterByWatchfaceId(
        watchfaceId: String?,
        precondition: () -> Boolean
    ) = if (precondition() && watchfaceId != null) {
        this.filter { it.feature.isWatchface() && it.feature.id != watchfaceId }
    } else {
        this
    }

    override fun retryLoading() = Unit

    fun enableFeature(feature: SuuntoPlusFeature) = setFeatureEnabled(feature, true)

    fun disableFeature(feature: SuuntoPlusFeature) = setFeatureEnabled(feature, false)

    fun confirmBeforeUninstallFeature(feature: SuuntoPlusFeature) {
        _showUninstallConfirmDialog.value = true
        currentUninstallFeature = feature
    }

    fun uninstallFeatureAfterConfirm() {
        currentUninstallFeature?.let {
            _showUninstallConfirmDialog.value = false
            disableFeature(it)
        }
    }

    fun uninstallFeatureCancel() {
        _showUninstallConfirmDialog.value = false
        currentUninstallFeature = null
    }

    private fun setFeatureEnabled(feature: SuuntoPlusFeature, enabled: Boolean) {
        launch {
            try {
                setEnabledStateForFeatureUseCase.updateEnabledState(feature.id, enabled)
                triggerSyncWithDelay(coroutineScope = this, alwaysTriggerWatchSync = true)
                suuntoPlusGuideAnalyticsUtils.sendMySportsAppsUseInWatchToggledEvent(feature.name, enabled)
                userMadeChanges = true
                syncRequired.value = true
            } catch (e: Exception) {
                Timber.w(e, "Failed to update enabled flag for a SuuntoPlus Feature")
            }
        }
    }

    fun syncWithRemote(alwaysTriggerWatchSync: Boolean = isDelayedSyncRequested()) {
        // Enable alwaysTriggerWatchSync by default in case user has toggled any of the enabled switches
        syncRequired.value = false
        syncNow(alwaysTriggerWatchSync = alwaysTriggerWatchSync)
    }

    fun resetSyncRequiredFlag() {
        syncRequired.value = false
    }

    fun sendSelectionScreenEvent() {
        launch(NonCancellable) {
            suuntoPlusGuideAnalyticsUtils.sendFeaturesSelectionScreenEvent()
        }
    }

    fun sendUserMadeChangesEventIfNeeded() {
        if (userMadeChanges) {
            launch(NonCancellable) {
                suuntoPlusGuideAnalyticsUtils.sendUserMadeChangesToFeaturesEvent()
            }
        }

        userMadeChanges = false
    }

    override fun onCleared() {
        super.onCleared()
        loadDataJob?.cancel()
        clearDelayedSync()
    }

    private fun List<SuuntoPlusFeatureWithStatus>.sorted() = sortedWith(compareBy(collator) { it.feature.name })
}
