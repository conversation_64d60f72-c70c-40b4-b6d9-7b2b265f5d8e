package com.stt.android.device.domain

import androidx.compose.runtime.Immutable

@Immutable
data class WatchAndNetworkNotificationState(
    val internetAvailable: Boolean,
    val watchPaired: Boolean,
    val watchBusy: Boolean,
    val watchSyncing: <PERSON><PERSON>an,
    val watchDisconnected: Bo<PERSON>an,
    val areSuuntoPlusGuidesSupported: Boolean?,
    val customToastMessage: String?,
    val customToastActionText: String?,
    val customErrorMessage: String?
) {
    companion object {
        val DEFAULT = WatchAndNetworkNotificationState(
            internetAvailable = true,
            watchPaired = true,
            watchBusy = false,
            watchSyncing = false,
            watchDisconnected = false,
            areSuuntoPlusGuidesSupported = null,
            customToastMessage = null,
            customToastActionText = null,
            customErrorMessage = null,
        )
    }
}
