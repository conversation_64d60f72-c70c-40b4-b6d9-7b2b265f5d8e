package com.stt.android.device.watchface

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.stt.android.common.navigation.findNavController
import com.stt.android.device.R
import com.stt.android.device.databinding.ActivitySuuntoOnlineWatchfaceBinding
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class SuuntoOnlineWatchFaceActivity : AppCompatActivity() {

    private lateinit var binding: ActivitySuuntoOnlineWatchfaceBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySuuntoOnlineWatchfaceBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val navController = findNavController(R.id.nav_host_fragment_container)
        navController.setGraph(R.navigation.suunto_online_watchface_nav, intent.extras)
    }
}
