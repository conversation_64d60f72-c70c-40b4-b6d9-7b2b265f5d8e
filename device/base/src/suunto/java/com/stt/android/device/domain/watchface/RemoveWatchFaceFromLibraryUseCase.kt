package com.stt.android.device.domain.watchface

import com.stt.android.device.datasource.watchface.WatchFaceLocalDataSource
import com.stt.android.device.remote.watchface.WatchFaceRemoteDataSource
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import timber.log.Timber
import javax.inject.Inject

class RemoveWatchFaceFromLibraryUseCase @Inject constructor(
    private val localDataSource: WatchFaceLocalDataSource,
    private val remoteDataSource: WatchFaceRemoteDataSource,
) {
    suspend fun removeWatchFaceFromWatch(
        id: String,
        watchFaceCapabilities: String,
        addToFavorite: Boolean
    ) {
        runCatching {
            localDataSource.updateAddToWatchEnabled(id, false)
            remoteDataSource.updateEnabledState(
                id = id,
                capabilities = watchFaceCapabilities,
                addToFavorite = addToFavorite,
                addToWatch = false
            )
        }.onFailure {
            Timber.w(it, "Failed to disable id $id")
        }
    }

    suspend fun removeWatchFaceFromLibrary(
        id: String,
        watchFaceCapabilities: String,
        addToWatch: Boolean
    ) {
        withContext(Dispatchers.IO) {
            remoteDataSource.addToLibrary(
                id = id,
                watchFaceCapabilities = watchFaceCapabilities,
                addToFavorite = false,
                addToWatch = addToWatch
            )
            localDataSource.deleteByIds(listOf(id))
        }
    }
}
