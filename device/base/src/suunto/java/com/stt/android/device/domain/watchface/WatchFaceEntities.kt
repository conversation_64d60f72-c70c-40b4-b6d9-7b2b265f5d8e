package com.stt.android.device.domain.watchface

import android.os.Parcelable
import androidx.compose.runtime.Immutable
import com.stt.android.data.source.local.watchface.WatchFaceSyncState
import com.stt.android.device.watchface.MyWatchFaceViewModel
import kotlinx.collections.immutable.ImmutableList
import kotlinx.parcelize.Parcelize

data class WatchFaceListContainer(
    val id: String,
    val title: String,
    val description: String?,
    val bannerImageUrl: String?,
    val itemList: List<WatchFace>?,
)

data class WatchFaceWithStatus(
    val watchFace: WatchFace,
    val status: WatchFaceStatus,
)

@Parcelize
data class MyWatchFaceList(
    val id: String,
    val title: String,
    val description: String?,
    val bannerImageUrl: String?,
    val itemList: List<WatchFace>?,
) : Parcelable

@Parcelize
data class WatchFace(
    val id: String,
    val category: String,
    val type: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val richText: String?,
    val currentVersion: String,
    val watchCapability: String,
    val watchfaceId: String,
    val labels: List<String>?,
    val iconUrl: String?,
    val tileBannerUrl: String?,
    val useDefaultImages: Boolean,
    val supported: Boolean,
    val addToFavorite: Boolean,
    val addToWatch: Boolean,
    val updated: Boolean,
) : Parcelable

@Parcelize
data class WatchFaceEntity(
    val runFeatureCatalogueId: String,
    val name: String,
    val description: String,
    val shortDescription: String?,
    val labels: List<String>?,
    val watchfaceId: String,
    val richText: String?,
    val iconUrl: String?,
    val tileBannerUrl: String?,
    val iconName: String,
    val supported: Boolean,
    val updated: Boolean,
    val addToWatch: Boolean,
    val addToFavorite: Boolean,
    val type: String,
    val fileSize: Long,
    val md5: String,
    val latestInstalledVersion: String?,
    val targetInstallVersion: String,
    val latestInstalledCapability: String?,
    val targetInstallCapability: String,
) : Parcelable

@Parcelize
data class UserLibraryWatchFaces(
    val libWatchFaces: List<WatchFace>,
) : Parcelable

@Parcelize
data class InstallWatchFace(
    val watchFaceId: String,
    val name: String,
    val installCapability: String,
    val installVersion: String,
    val preImgName: String,
    val fileSize: Long,
    val md5: String,
) : Parcelable

@Immutable
data class MyWatchFacesListContainer(
    val watchFaces: ImmutableList<WatchFaceWithStatus>,
    val syncState: WatchFaceSyncState,
    val watchBusy: Boolean,
    val watchDisconnected: Boolean,
    val showSyncNowButton: Boolean,
    val showWatchFullNotification: Boolean,
    val canEnableMoreFeatures: Boolean,
    val watchFacesOnWatchCount: Int?,
    val watchFacesOnWatchCountMax: Int?,
    val currentWatchFaceId: String?,
    val notificationState: MyWatchFaceViewModel.Notification,
)

fun WatchFaceEntity.toInstallWatchFace() = InstallWatchFace(
    watchFaceId = watchfaceId,
    name = name,
    installCapability = targetInstallCapability,
    installVersion = targetInstallVersion,
    preImgName = iconName,
    fileSize = fileSize,
    md5 = md5,
)
