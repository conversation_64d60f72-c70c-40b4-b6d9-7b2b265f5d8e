package com.stt.android.device.watchface

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.stt.android.data.source.local.watchface.WatchFaceSyncState
import com.stt.android.device.domain.watchface.IsWatchFaceSyncOngoingUseCase
import com.stt.android.watch.background.WatchFaceRemoteSyncJobLauncher
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

@HiltViewModel
class PullToRemoteSyncWatchFaceViewModel @Inject constructor(
    private val remoteSyncJobLauncher: WatchFaceRemoteSyncJobLauncher,
    private val isSyncOngoingUseCase: IsWatchFaceSyncOngoingUseCase,
) : ViewModel() {
    private val remoteSyncPending = MutableStateFlow(false)
    private var currentSyncState = WatchFaceSyncState.IDLE
    private var userInitiatedSync = MutableStateFlow(false)

    // Show pull-to-refresh indicator while waiting for pending remote sync to start and while the
    // sync is running.
    val refreshing: StateFlow<Boolean> =
        combine(remoteSyncPending, isSyncOngoingUseCase.getSyncStateFlow(), userInitiatedSync, ::Triple)
            .map { (pending, state, userInitiatedSync) ->
                pending || (userInitiatedSync && state == WatchFaceSyncState.REMOTE_SYNC_ONGOING)
            }
            .stateIn(
                scope = viewModelScope,
                started = SharingStarted.Eagerly,
                initialValue = false
            )

    init {
        viewModelScope.launch {
            isSyncOngoingUseCase.getSyncStateFlow()
                .debounce(150L) // debounce to make sure new sync state has handled before resetting remoteSyncPending
                .collect { state ->
                    if (currentSyncState == WatchFaceSyncState.REMOTE_SYNC_ONGOING && state != WatchFaceSyncState.REMOTE_SYNC_ONGOING) {
                        // User initiated sync ended, reset flag
                        userInitiatedSync.value = false
                    }

                    currentSyncState = state
                    if (state == WatchFaceSyncState.REMOTE_SYNC_ONGOING) {
                        // Reset remoteSyncPending flag when remote sync actually starts
                        swipeToRefreshTimeoutJob?.cancel()
                        swipeToRefreshTimeoutJob = null
                        remoteSyncPending.value = false
                    }
                }
        }
    }

    private var swipeToRefreshTimeoutJob: Job? = null

    fun syncRemote(alwaysTriggerWatchSync: Boolean = false) {
        // Pull-to-refresh tries to start remote sync, but in some cases the sync job may refuse to
        // start. This occurs if watch sync is ongoing currently, for example. Add a safety
        // mechanism so we don't get stuck showing the refresh progress indicator.
        remoteSyncPending.value = true
        userInitiatedSync.value = true
        remoteSyncJobLauncher.enqueueRemoteSyncJob(alwaysTriggerWatchSync)
        swipeToRefreshTimeoutJob?.cancel()
        swipeToRefreshTimeoutJob = viewModelScope.launch {
            delay(SWIPE_REFRESH_SYNC_START_TIMEOUT)
            if (currentSyncState != WatchFaceSyncState.REMOTE_SYNC_ONGOING) {
                // Reset remoteSyncPending flag when sync job startup timed out
                Timber.d("Remote sync job didn't start immediately, resetting pending flag")
                remoteSyncPending.value = false
            }
        }
    }

    companion object {
        private const val SWIPE_REFRESH_SYNC_START_TIMEOUT = 4000L
    }
}
