package com.stt.android.device.suuntoplusfeature

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.animation.Crossfade
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.MaterialTheme
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.livedata.observeAsState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import com.stt.android.compose.modifiers.narrowContent
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.widgets.ProgressToast
import com.stt.android.compose.widgets.Toast
import com.stt.android.data.source.local.suuntoplusguide.SuuntoPlusSyncState
import com.stt.android.device.R
import com.stt.android.device.domain.suuntoplusfeature.SuuntoPlusFeature
import com.stt.android.device.suuntoplusfeature.listitems.SyncNowButton
import com.stt.android.utils.CustomTabsUtils
import com.stt.android.watch.SuuntoPlusStoreNavigator
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.collections.immutable.toImmutableList
import javax.inject.Inject
import com.stt.android.R as BaseR
import com.stt.android.core.R as CR

@AndroidEntryPoint
class SuuntoPlusFeaturesListFragment : Fragment() {

    private val viewModel: SuuntoPlusFeaturesListViewModel by viewModels()
    private val pullToSyncViewModel: PullToRemoteSyncSuuntoPlusViewModel by viewModels()
    private val args: SuuntoPlusFeaturesListFragmentArgs by navArgs()

    private enum class Notification {
        NONE,
        SYNCING,
        BUSY,
        DISCONNECTED,
        SYNC_NOW_BUTTON,
        WATCH_FULL,
    }

    @Inject
    lateinit var suuntoPlusStoreNavigator: SuuntoPlusStoreNavigator

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        viewModel.loadData(args.excludedWatchface, args.onlyWatchface)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setHasOptionsMenu(true)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setViewCompositionStrategy(
                ViewCompositionStrategy.DisposeOnLifecycleDestroyed(viewLifecycleOwner)
            )

            setContent {
                AppTheme {
                    val refreshing by pullToSyncViewModel.refreshing.collectAsState()
                    val viewState by viewModel.viewState.observeAsState()
                    val isShowUninstallConfirmDialog by viewModel.showUninstallConfirmDialog.observeAsState()

                    Box(
                        contentAlignment = Alignment.TopCenter,
                        modifier = Modifier
                            .background(MaterialTheme.colors.background)
                            .narrowContent(),
                    ) {
                        viewState?.data?.let { data ->
                            SuuntoPlusFeaturesList(
                                features = data.features.toImmutableList(),
                                currentTimeMillis = data.currentTimeMillis,
                                canEnableMoreFeatures = data.canEnableMoreFeatures,
                                isShowConfirmDialog = isShowUninstallConfirmDialog ?: false,
                                featuresOnWatchCount = data.featuresOnWatchCount,
                                featuresOnWatchCountMax = data.featuresOnWatchCountMax,
                                onFeatureClick = ::showFeatureDetails,
                                enableInstallActions = !data.watchBusy && data.syncState == SuuntoPlusSyncState.IDLE,
                                syncOngoing = data.syncState.isSyncOngoing,
                                onInstallFeature = viewModel::enableFeature,
                                onUninstallFeature = viewModel::disableFeature,
                                onShowUninstallConfirmDialog = viewModel::confirmBeforeUninstallFeature,
                                onConfirmUninstallFeature = viewModel::uninstallFeatureAfterConfirm,
                                onConfirmCancel = viewModel::uninstallFeatureCancel,
                                onTutorialClick = ::showUserGuide,
                                onPlusStoreLinkClick = ::showSuuntoPlusStore,
                                refreshing = refreshing,
                                enablePullToRefresh = data.syncState == SuuntoPlusSyncState.IDLE,
                                onRefresh = {
                                    viewModel.resetSyncRequiredFlag()
                                    pullToSyncViewModel.syncRemote()
                                },
                                isWatchfaceList = args.onlyWatchface,
                                currentWatchfaceId = data.currentWatchfaceId,
                                isWatchfaceSupported = data.isWatchfaceSupported,
                                isRunDevice = data.isRunDevice,
                            )

                            Crossfade(
                                targetState = data.notificationState,
                                modifier = Modifier
                                    .padding(MaterialTheme.spacing.medium)
                                    .widthIn(max = dimensionResource(id = CR.dimen.dialog_max_width))
                                    .fillMaxWidth()
                            ) {
                                Box(
                                    contentAlignment = Alignment.TopCenter,
                                    modifier = Modifier.fillMaxWidth()
                                ) {
                                    when (it) {
                                        Notification.SYNCING -> ProgressToast(
                                            text = stringResource(id = BaseR.string.sport_mode_watch_syncing)
                                        )

                                        Notification.BUSY -> Toast(
                                            text = stringResource(id = BaseR.string.suunto_plus_sync_watch_busy)
                                        )

                                        Notification.DISCONNECTED -> Toast(
                                            text = stringResource(id = BaseR.string.sport_mode_watch_disconnected)
                                        )

                                        Notification.SYNC_NOW_BUTTON -> SyncNowButton(
                                            onClick = {
                                                viewModel.syncWithRemote(alwaysTriggerWatchSync = true)
                                            }
                                        )

                                        Notification.WATCH_FULL -> if (args.onlyWatchface) {
                                            SuuntoPlusFeaturesMaxUsageReached(stringResource(R.string.suunto_plus_reached_maximum_amount_of_watch_faces_on_watch))
                                        } else {
                                            SuuntoPlusFeaturesMaxUsageReached(stringResource(R.string.suunto_plus_features_max_usage_reached))
                                        }

                                        Notification.NONE -> {}
                                    }
                                }
                            }
                        }

                        if (viewState?.isLoading() == true && viewState?.data == null) {
                            CircularProgressIndicator(
                                modifier = Modifier.align(Alignment.Center)
                            )
                        }
                    }
                }
            }
        }
    }

    override fun onStart() {
        super.onStart()

        viewModel.sendSelectionScreenEvent()
    }

    override fun onPause() {
        super.onPause()
        viewModel.syncNowIfDelayedSyncRequested()
        viewModel.sendUserMadeChangesEventIfNeeded()
    }

    private fun showUserGuide() {
        CustomTabsUtils.launchCustomTab(
            requireContext(),
            getString(R.string.suunto_plus_feature_selection_user_guide_url)
        )
    }

    private fun showSuuntoPlusStore() {
        startActivity(suuntoPlusStoreNavigator.newSuuntoPlusStoreIntent(requireContext()))
    }

    private fun showFeatureDetails(feature: SuuntoPlusFeature) = with(findNavController()) {
        if (currentDestination?.id == R.id.featureListFragment) {
            navigate(
                SuuntoPlusFeaturesListFragmentDirections.actionFromFeatureListToDetails(
                    featureId = feature.id,
                    title = feature.name
                )
            )
        }
    }

    private val SuuntoPlusFeaturesListContainer.notificationState: Notification
        get() = when {
            syncState == SuuntoPlusSyncState.WATCH_SYNC_ONGOING -> Notification.SYNCING
            watchBusy -> Notification.BUSY
            watchDisconnected -> Notification.DISCONNECTED
            showSyncNowButton -> Notification.SYNC_NOW_BUTTON
            showWatchFullNotification -> Notification.WATCH_FULL
            else -> Notification.NONE
        }
}
