package com.stt.android.compose.widgets

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material.Card
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.material.LocalContentColor
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.material3.CardDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.stt.android.compose.theme.AppTheme
import com.stt.android.compose.theme.mediumGrey
import com.stt.android.compose.theme.spacing
import com.stt.android.compose.theme.toastShape
import kotlinx.coroutines.delay
import java.util.Locale
import androidx.compose.material3.MaterialTheme as M3Theme
import androidx.compose.material3.Text as M3Text
import androidx.compose.material3.TextButton as M3TextButton
import androidx.compose.material3.Card as M3Card

@Composable
fun Toast(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colors.mediumGrey,
    contentColor: Color = MaterialTheme.colors.onPrimary,
) {
    Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentColor = contentColor,
    ) {
        Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(MaterialTheme.spacing.small),
        )
    }
}

@Composable
fun M3Toast(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = M3Theme.colorScheme.mediumGrey,
    contentColor: Color = M3Theme.colorScheme.onPrimary,
) {
    M3Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentColor = contentColor,
    ) {
        M3Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(MaterialTheme.spacing.small),
        )
    }
}

@Composable
fun ProgressToast(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colors.mediumGrey,
) {
    Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentPadding = PaddingValues(0.dp)
    ) {
        // Compose circular progress indicator is fixed at 40.dp size and our design calls for 24.dp.
        // Scale it down to match the target.
        Row(
            modifier = Modifier
                .widthIn(min = DefaultToastHeight)
                .animateContentSize(animationSpec = spring(stiffness = Spring.StiffnessLow))
        ) {
            CircularProgressIndicator(
                color = LocalContentColor.current,
                modifier = Modifier
                    .padding(start = (DefaultToastHeight - 40.dp) / 2)
                    .scale(24.0f / 40.0f),
            )

            val showText = remember { mutableStateOf(false) }
            LaunchedEffect(Unit) {
                delay(400L)
                showText.value = true
            }

            if (showText.value) {
                Text(
                    text = text,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.xsmall,
                        top = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.small
                    )
                )
            }
        }
    }
}

@Composable
fun M3ProgressToast(
    text: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color = M3Theme.colorScheme.mediumGrey,
) {
    M3Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentPadding = PaddingValues(0.dp)
    ) {
        // Compose circular progress indicator is fixed at 40.dp size and our design calls for 24.dp.
        // Scale it down to match the target.
        Row(
            modifier = Modifier
                .widthIn(min = DefaultToastHeight)
                .animateContentSize(animationSpec = spring(stiffness = Spring.StiffnessLow))
        ) {
            androidx.compose.material3.CircularProgressIndicator(
                color = androidx.compose.material3.LocalContentColor.current,
                modifier = Modifier
                    .padding(start = (DefaultToastHeight - 40.dp) / 2)
                    .scale(24.0f / 40.0f),
            )

            val showText = remember { mutableStateOf(false) }
            LaunchedEffect(Unit) {
                delay(400L)
                showText.value = true
            }

            if (showText.value) {
                M3Text(
                    text = text,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(
                        start = MaterialTheme.spacing.xsmall,
                        top = MaterialTheme.spacing.small,
                        end = MaterialTheme.spacing.medium,
                        bottom = MaterialTheme.spacing.small
                    )
                )
            }
        }
    }
}

@Composable
fun ErrorToast(
    text: String,
    modifier: Modifier = Modifier,
) {
    Toast(
        backgroundColor = MaterialTheme.colors.error,
        contentColor = MaterialTheme.colors.onError,
        modifier = modifier
    ) {
        Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(
                    vertical = MaterialTheme.spacing.medium,
                    horizontal = MaterialTheme.spacing.small
                ),
        )
    }
}

@Composable
fun M3ErrorToast(
    text: String,
    modifier: Modifier = Modifier,
) {
    Toast(
        backgroundColor = M3Theme.colorScheme.error,
        contentColor = M3Theme.colorScheme.onError,
        modifier = modifier
    ) {
        M3Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .padding(
                    vertical = MaterialTheme.spacing.medium,
                    horizontal = MaterialTheme.spacing.small
                ),
        )
    }
}

@Composable
fun Toast(
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colors.mediumGrey,
    contentColor: Color = MaterialTheme.colors.onPrimary,
    contentPadding: PaddingValues = PaddingValues(horizontal = MaterialTheme.spacing.small),
    content: @Composable RowScope.() -> Unit = {}
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopCenter,
    ) {
        Card(
            backgroundColor = backgroundColor,
            contentColor = contentColor,
            shape = MaterialTheme.shapes.toastShape,
            elevation = 8.dp,
        ) {
            Row(
                modifier = Modifier
                    .heightIn(min = DefaultToastHeight)
                    .padding(contentPadding),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                content()
            }
        }
    }
}

@Composable
fun M3Toast(
    modifier: Modifier = Modifier,
    backgroundColor: Color = M3Theme.colorScheme.mediumGrey,
    contentColor: Color = M3Theme.colorScheme.onPrimary,
    contentPadding: PaddingValues = PaddingValues(horizontal = MaterialTheme.spacing.small),
    content: @Composable RowScope.() -> Unit = {}
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.TopCenter,
    ) {
        M3Card(
            colors = CardDefaults.cardColors().copy(
                containerColor = backgroundColor,
                contentColor = contentColor
            ),
            shape = M3Theme.shapes.toastShape,
            elevation = CardDefaults.cardElevation(
                defaultElevation = 8.dp
            ),
        ) {
            Row(
                modifier = Modifier
                    .heightIn(min = DefaultToastHeight)
                    .padding(contentPadding),
                verticalAlignment = Alignment.CenterVertically,
            ) {
                content()
            }
        }
    }
}

@Composable
fun ActionableToast(
    text: String,
    buttonText: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = MaterialTheme.colors.primary,
    contentColor: Color = MaterialTheme.colors.onPrimary
) {
    Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentColor = contentColor
    ) {
        Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(MaterialTheme.spacing.small),
        )

        Spacer(modifier = Modifier.weight(1f))

        TextButton(onClick = onClick) {
            Text(
                text = buttonText.uppercase(Locale.getDefault()),
                color = contentColor
            )
        }
    }
}

@Composable
fun M3ActionableToast(
    text: String,
    buttonText: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = M3Theme.colorScheme.primary,
    contentColor: Color = M3Theme.colorScheme.onPrimary
) {
    M3Toast(
        modifier = modifier,
        backgroundColor = backgroundColor,
        contentColor = contentColor
    ) {
        M3Text(
            text = text,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(MaterialTheme.spacing.small),
        )

        Spacer(modifier = Modifier.weight(1f))

        M3TextButton(onClick = onClick) {
            M3Text(
                text = buttonText.uppercase(Locale.getDefault()),
                color = contentColor
            )
        }
    }
}

private val DefaultToastHeight = 50.dp

@Preview
@Composable
private fun TextToastPreview() {
    AppTheme {
        Toast("No internet connection")
    }
}

@Preview
@Composable
private fun ErrorToastPreview() {
    AppTheme {
        ErrorToast("Watch not compatible with SuuntoPlus™ guides to sync workouts")
    }
}

@Preview
@Composable
private fun ActionableToastPreview() {
    AppTheme {
        ActionableToast(
            text = "Saved to My apps",
            buttonText = "View",
            onClick = {}
        )
    }
}

@Preview(widthDp = 320, heightDp = 50)
@Composable
private fun ProgressToastEnterTransitionPreview() {
    AppTheme {
        Box(contentAlignment = Alignment.TopCenter, modifier = Modifier.fillMaxWidth()) {
            val visible = remember { mutableStateOf(true) }

            LaunchedEffect(Unit) {
                repeat(1000) {
                    delay(2500L)
                    visible.value = !visible.value
                }
            }

            AnimatedVisibility(
                visible = visible.value,
                enter = fadeIn(),
                exit = fadeOut(),
            ) {
                ProgressToast("Watch is syncing")
            }
        }
    }
}
