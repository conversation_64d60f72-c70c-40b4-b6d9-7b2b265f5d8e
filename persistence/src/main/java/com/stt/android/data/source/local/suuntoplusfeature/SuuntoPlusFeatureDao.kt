package com.stt.android.data.source.local.suuntoplusfeature

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import com.stt.android.data.source.local.TABLE_SUUNTO_PLUS_FEATURES
import kotlinx.coroutines.flow.Flow
import timber.log.Timber

@Dao
abstract class SuuntoPlusFeatureDao {
    @Transaction
    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_FEATURES")
    abstract fun fetchAllWithStateAsFlow(): Flow<List<LocalSuuntoPlusFeatureWithDeviceStatus>>

    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_FEATURES")
    abstract fun fetchAllAsFlow(): Flow<List<LocalSuuntoPlusFeature>>

    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_FEATURES WHERE id = :featureId")
    abstract suspend fun findById(featureId: String): LocalSuuntoPlusFeature?

    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_FEATURES WHERE id = :featureId")
    abstract fun findByIdAsFlow(featureId: String): Flow<LocalSuuntoPlusFeature?>

    @Transaction
    @Query("SELECT * FROM $TABLE_SUUNTO_PLUS_FEATURES WHERE id = :featureId")
    abstract fun findFeatureWithStateAsFlow(featureId: String): Flow<LocalSuuntoPlusFeatureWithDeviceStatus?>

    @Insert
    abstract suspend fun insert(feature: LocalSuuntoPlusFeature)

    @Update
    abstract suspend fun update(guide: LocalSuuntoPlusFeature)

    @Query("UPDATE $TABLE_SUUNTO_PLUS_FEATURES SET enabled = :enabled WHERE id = :featureId")
    abstract suspend fun updateEnabledState(featureId: String, enabled: Boolean): Int

    @Query("UPDATE $TABLE_SUUNTO_PLUS_FEATURES SET enabled = :enabled WHERE id IN (:featureIds)")
    abstract suspend fun updateEnabledState(featureIds: Collection<String>, enabled: Boolean): Int

    @Query("UPDATE $TABLE_SUUNTO_PLUS_FEATURES SET manifest = :manifestJson WHERE id = :featureId")
    abstract suspend fun updateManifestJson(featureId: String, manifestJson: String?): Int

    @Transaction
    open suspend fun upsert(feature: LocalSuuntoPlusFeature) {
        val oldFeature = findById(feature.id)
        if (oldFeature == null) {
            Timber.d("Insert new feature, id=${feature.id}")
            insert(feature)
        } else {
            Timber.d("Update existing feature, id=${feature.id}")
            if (feature.manifestJson == null && oldFeature.manifestJson != null) {
                // Backend doesn't return the manifest JSON for features and it needs to be
                // explicitly unzipped from the plug-in ZIP file. Add a safeguard here to ensure
                // we don't lose the manifest JSON when the feature is updated.
                update(feature.copy(manifestJson = oldFeature.manifestJson))
            } else {
                update(feature)
            }
        }
    }

    @Query("DELETE FROM $TABLE_SUUNTO_PLUS_FEATURES WHERE id IN (:featureIds)")
    abstract suspend fun deleteByIds(featureIds: List<String>)

    // Return true if either a) any feature has a 'pluginId' value, or b) the table is empty
    @Query(
        """
        SELECT
            (SELECT COUNT(*) FROM $TABLE_SUUNTO_PLUS_FEATURES WHERE plugin_id IS NOT NULL) > 0 OR
            (SELECT COUNT(*) FROM $TABLE_SUUNTO_PLUS_FEATURES) = 0
    """
    )
    abstract suspend fun havePluginIds(): Boolean

    @Query("DELETE FROM $TABLE_SUUNTO_PLUS_FEATURES")
    abstract suspend fun deleteAll()
}
